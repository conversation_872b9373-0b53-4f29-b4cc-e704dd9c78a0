#%% md
# Daten standardisieren [15P]

#%%
import numpy as np
#%%
rng = np.random.default_rng(42)
#%% md
Sie erhalten ein NumPy-Array und sollen die Daten so standardisieren, dass der Mittelwert bei `0` liegt und die Varianz (genauso wie die Standardabweichung) `1` beträgt. Aus der Computerübung kennen Sie bereits:

#%%
array = np.arange(0, 11, 2)
array_norm = (array - array.mean()) / array.std()
array, array_norm
#%% md
Es ist ratsam, erst den Mittelwert auf `0` zu setzen, da das Intervall, welches durch die Standardabweichung eingeschlossen wird, von `mean - std` bis `mean + std` geht und `mean == 0` uns erlaubt, den Mittelwert bei der Skalierung zu ignorieren.

Ihre Aufgabe ist es nun, diese Standardisierung ohne Verwendung von `np.mean` (bzw. `.mean`), `np.std` (bzw. `.std`) und `np.var` (bzw. `.var`) zu realisieren.

Schreiben Sie hierfür eine Funktion `standardization`

#%% md
Ansonsten sollte Ihr Code folgenden Anforderungen genügen:

#%% md
- verwendet idiomatische Numpy-Methoden sinnvoll

#%% md
- ist gut lesbar und sinnvoll dokumentiert

#%% md
- ist performant und skaliert auch mit "großen" Eingabedaten gut

#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%%
standardization(array)
#%%
array = rng.integers(0, 10, 100)
array, standardization(array)
#%%
array = rng.integers(-10, 5, 100_000)
array, standardization(array)
#%%
array = rng.normal(5, 2, 100)
array, standardization(array)
#%% md
Test mit standardverteilten Daten:
#%%
array = rng.normal(0, 1, 100_000_000)

(standardization(array) - array)