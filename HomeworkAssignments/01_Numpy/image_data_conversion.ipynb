#%% md
# Bilddaten konvertieren [10P]

#%%
import matplotlib.pyplot as plt
import numpy as np
#%% md
Sie erhalten ein NumPy-Array, das ein Bild darstellt. Dieses Bild hat $N\times M$ Pixel und ist mit drei Farbkanälen in Form eines `(3, N, M)`-<PERSON><PERSON><PERSON> von Integern zwischen 0 and 255 abgespeichert.

Sie wollen dieses Bild mit `matplotlib.plt.imshow` darstellen. Leider kann diese Funktion nur mit Arrays der Form `(N, M, 3)` umgehen. Konvertieren Sie das Bild für die Darstellung mit `matplotlib.plt.imshow`. Schreiben Sie die konvertierten Bilddaten auf ein Array `converted_image` so, dass es unten richtig dargestellt wird. Dann ist in der linken unteren Ecke ein grüner Haken zu sehen.

Ansonsten sollte Ihr Code folgenden Anforderungen genügen:

#%% md
- verwendet idiomatische Numpy-Methoden sinnvoll

#%% md
- ist gut lesbar und sinnvoll dokumentiert

#%% md
- ist performant und skaliert auch mit "großen" Eingabedaten gut

#%%
image = np.load('image.npy')
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%%
plt.imshow(converted_image)