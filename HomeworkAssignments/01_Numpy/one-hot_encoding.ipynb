#%% md
# One-hot encoding [15P]

#%%
import numpy as np
#%% md
"One-Hot Encoding" wird unter anderem im Machine Learning verwendet, um kategorische Daten zu repräsentieren. Dabei wird ein Array mit $n$ Einträgen, die jeweils einer aus $m$ verschiedenen Kategorien zugeordnet sind, als $n\times m$-Array repräsentiert. Dabei enthält jede Zeile des Arrays genau eine 1 und ansonsten 0. Zusätzlich wird noch ein 1D-Array der vorhandenen Kategorien erzeugt.

Beispielsweise wird das Array `[1, 5, 1, 2]` in folgender Weise kodiert:

|     |     | 1   | 5   | 2   |
| --- | --- | --- | --- | --- |
| 1   | ->  | 1   | 0   | 0   |
| 5   | ->  | 0   | 1   | 0   |
| 1   | ->  | 1   | 0   | 0   |
| 2   | ->  | 0   | 0   | 1   |

Schreiben Sie eine Funktion `one_hot_encoder`, die aus einem 1D-Array mit beliebigen (aber numerischen) Daten eine "One-Hot-Kodierung" dieser Daten erstellt. Die Funktion soll ein 1D-Array der Kategorien (hier `[1, 5, 2]`) und ein 2D-Array für das Encoding (hier das $4\times3$-Array mit den Werten 0 und 1) zurückgeben. (Die Sortierung der Kategorien soll dabei nicht vorgegeben sein, also auch `[1, 2, 5]` möglich).

Ansonsten soll Ihr Code folgenden Anforderungen genügen:

#%% md
- verwendet idiomatische Numpy-Methoden sinnvoll

#%% md
- ist gut lesbar und sinnvoll dokumentiert

#%% md
- ist performant und skaliert auch mit "großen" Eingabedaten gut

#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%%
# Usage example: Code a feature vector (example above [1, 5, 1, 2] )
one_hot_encoder(np.array([1,5,1,2]))
#%%
# Usage example: Code a feature vector (random)
a = np.random.randint(10, size=10)
print(a)
one_hot_encoder(a)