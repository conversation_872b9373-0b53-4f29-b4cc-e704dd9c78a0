#%% md
## Sinnvolle Darstellung von Series [20 P]

Gegeben sind drei `pandas.Series` mit unterschiedlichen Inhalten. Stellen Sie diese Daten jeweils in einfachen Plots *sinnvoll* dar:
* geben Sie dem Plot einen aussagekräftigen Titel
* beschriften Sie ggf. die Achsen
* skalieren Sie ggf. die Achsen, um alle Werte oder ihren funktionalen Zusammenhang darstellen zu können
* erzeugen Sie ggf. eine Legende
#%%
%matplotlib inline
import numpy as np
import pandas as pd
from matplotlib import pyplot as plt
#%% md
### Aktinide
`actinides` enthält die Halbwertszeiten einer bestimmten Gruppe von chemischen Elementen ("Aktinide"), die allesamt radioaktiv sind. Angegeben ist jeweils die Halbwertszeit des langlebigsten Isotops, in Jahren.
#%%
actinides = pd.read_csv('data/actinides.csv', index_col=0).squeeze()
actinides
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%% md
### Aktienpreis
`stock_price` enthält den mit geometrischer brownscher Bewegung simulierten Preis einer Aktie seit 2019, in €.
#%%
stock_price = pd.read_csv(
    'data/stock_price.csv', index_col=0, parse_dates=True
).squeeze()
stock_price
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%% md
### Bundestagswahl 2021
`waehlerstimmen` enthält die absolute Zahl der auf verschiedene Parteien entfallenen Zweitstimmen bei der Bundestagswahl 2021.
#%%
waehlerstimmen = pd.read_csv('data/waehlerstimmen.csv', index_col=0).squeeze()
waehlerstimmen
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%%
