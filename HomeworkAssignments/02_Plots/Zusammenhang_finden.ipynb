#%% md
## Funktionalen Zusammenhang finden [20 P]

Eine Probe eines neuartigen Materials "Unobtainiumoxid" wird in einer Vakuumkammer erhitzt. Dabei werden ihre thermischen Eigenschaften über einen weiten Temperaturbereich gemessen. Für verschiedene Temperaturen $T$ wird die thermische Leistung $P$ (Energieabgabe pro Zeit) des Materials aufgezeichnet.

Die Messpunkte finden Sie in der `pandas.Series` namens `thermal`. Der Index bezeichnet die Temperatur $T$ der Probe in Kelvin ($\text{K}$), die Werte die thermische Leistung $P$ in Watt ($\text{W}$). Beide Messgrößen sind mit einem (unbekannten) Fehler behaftet.
#%%
import numpy as np
import pandas as pd
from matplotlib import pyplot as plt
%matplotlib inline
#%%
thermal = pd.read_csv('data/thermal.csv', index_col=0).squeeze()
#%% md
### Grafische Darstellung
Stellen Sie die Messwerte grafisch dar. Nutzen Sie eine sinnvolle Darstellung mit Titel, Achsenbeschriftungen, Achsenskalierung und ggf. Legende.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
plt.scatter(thermal.index, thermal.values, color='red', alpha=0.7, label='Messwerte')
plt.title('Thermische Leistung von Unobtainiumoxid in Abhängigkeit von der Temperatur')
plt.xlabel('Temperatur (K)')
plt.ylabel('Thermische Leistung (W)')
plt.grid(True, alpha=0.3)
plt.legend()

plt.show()
#%% md
### Funktionaler Zusammenhang
Bestimmen Sie aus Ihrem Graphen: Welcher *funktionale Zusammenhang* besteht zwischen Temperatur der Probe und Leistungsabgabe
  * bei "geringer" Temperatur?
  * bei "hoher" Temperatur?
  
Sie müssen keine Vorfaktoren bestimmen. Es reicht, wenn Sie feststellen, ob die Leistungsabgabe mit steigender Temperatur linear, quadratisch, logarithmisch, exponentiell, oder auf sonstige Weise zunimmt.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
