#%% md
## Skalieren einer Series

Schreiben Sie eine Funktion `scale_series`, welche die Standardabweichung und den Mittelwert einer `pd.Series` auf gegebene Werte setzt. Die Funktion soll folgende Signatur haben:

```python
scale_series(numeric: pd.Series, new_mean: float=None, new_std: float=None) -> pd.Series
```
#%%
import numpy as np
import pandas as pd
#%% md
- **\[6 P\]** Die Funktion gibt eine neue pandas-Series zurück, welche den gleichen Index und die gleiche Anzahl an Werten hat, aber den Mittelwert `new_mean` und die Standardabweichung `new_std`. Die Verteilung der Werte um ist ansonsten unverändert, so wie auch die eingegebene Series.
#%% md
- **\[6 P\]** Die Funktion lässt Mittelwert und Standardabweichung unverändert, falls diese nicht als Argumente übergeben wurden.
#%% md
- **\[6 P\]** Insbesondere kann die Funktion Series mit weniger als zwei Einträgen verarbeiten. Überlegen Sie, ob und wie in diesen Fällen Mittelwert und Standardabweichung angepasst werden können.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%%
# Usage example 1:
s=pd.Series(np.random.rand(100))
print(f"before (count,mean,std): {s.count(),s.mean(),s.std()}")
s_scaled=scale_series(s,10.0,5.0)
print(f"after  (count,mean,std): {s_scaled.count(),s_scaled.mean(),s_scaled.std()}")
print(f"result should be (100, 10.0, 5.0)")
#%%
# Usage example 2:
s=pd.Series(np.random.rand(100))
print(f"before (count,mean,std): {s.count(),s.mean(),s.std()}")
s_scaled=scale_series(s)
print(f"after  (count,mean,std): {s_scaled.count(),s_scaled.mean(),s_scaled.std()}")
print(f"result should be the same as before")
#%%
# Usage example 3:
s=pd.Series(np.random.rand(100))
print(f"before (count,mean,std): {s.count(),s.mean(),s.std()}")
s_scaled=scale_series(s,new_mean=10.0)
print(f"after  (count,mean,std): {s_scaled.count(),s_scaled.mean(),s_scaled.std()}")
print(f"result should be the same as before, except new_mean=10.0")
#%%
# Usage example 4:
s=pd.Series(np.random.rand(100))
print(f"before (count,mean,std): {s.count(),s.mean(),s.std()}")
s_scaled=scale_series(s,new_std=5.0)
print(f"after  (count,mean,std): {s_scaled.count(),s_scaled.mean(),s_scaled.std()}")
print(f"result should be the same as before, except new_std=5.0")