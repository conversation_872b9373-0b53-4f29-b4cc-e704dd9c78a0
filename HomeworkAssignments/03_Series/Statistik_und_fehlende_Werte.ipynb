#%% md
# Statistik und fehlende Werte
Mit realen Datensätzen entsteht häufig die Problematik, dass Daten fehlen oder implizite Werte haben. In dieser Übung sollen Sie für einen Datensatz aus dem US-Zensus die Einkommensungleichheit berechnen und visualisieren.

Lesen Sie dazu zunächst die Einkommen aus dem US-Zensusdatensatz von 2017 ein:
#%%
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

%matplotlib inline

import pathlib
#%% md
## <span style="color:red">ACHTUNG:</span> Die Datei 'US_ACS_2017_10pct_sample.dta' nicht mit in der Lösung hochladen.
#%%
url = 'https://github.com/nickeubank/MIDS_Data/raw/master/US_AmericanCommunitySurvey/US_ACS_2017_10pct_sample.dta'
data_file = pathlib.Path(url.rsplit('/', 1)[-1])

if not data_file.is_file():
    # this downloads 63 MB of data. May take a few seconds.
    # We could also use `census_data = pd.read_stata(url)['inctot']`
    # but here we save the file to disk in case we need to reset it.
    urllib.request.urlretrieve(url, data_file)

census_data = pd.read_stata(data_file.resolve())['inctot']
#%% md
## Bereinigung von Daten
#%% md
**[2 Pt]** Berechnen Sie grundlegende statistische Indikatoren für Ihre Daten. Fällt Ihnen etwas auf? Nutzen Sie ggf. auch die Ergebnisse Ihrer Plots unten oder die Ergebnisse der Methode `Series.value_counts`.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%% md
**\[4 Pt\]** Die `Series` enthält eine ganze Menge an offensichtlich falschen Werten. Wie können Sie fehlende und offensichtlich falsche Werte ersetzen? Ersinnen Sie zwei unterschiedliche Strategien und setzen Sie diese um.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%% md
**\[4 Pt\]** Erstellen Sie eine sinnvolle Abbildung der Einkommensverteilung. Nutzen Sie eine von ihnen gewählte Strategie zur Ersetzung von falschen/fehlenden Werten.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%% md
## Einkommensungleichheit

### Die Lorenz-Kurve
Um die Ungleichheit der Einkommen zu visualisieren, bietet sich die sogenannte Lorenz-Kurve an. Hierbei werden die Werte zunächst sortiert und dann die kumulierte statistische Größe gegen den kumulierten Populationsanteil aufgetragen.

![image.png](attachment:image.png)
#%% md
**\[6 Pt\]** 
Verarbeiten Sie Ihre Daten so, dass sie einen entsprechenden Plot erzeugen können. Stellen Sie die kumulierte Einkommensverteilung (kumuliertes Einkommen gegen kumulierter Anteil der Bevölkerung) im Stile einer Lorenz-Kurve dar. Stellen Sie außerdem zum Vergleich eine perfekt ausgeglichene  und eine perfekt unausgeglichene Einkommensverteilung dar.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%% md
### Der Gini-Koeffizient
Der Gini-Koeffizient ist ein statistisches Maß für die ungleiche Verteilung einer nichtnegativen Größe. Er wird häufig verwendet, um Einkommens- oder Vermögensungleichheit in einer Gesellschaft oder zwischen Staaten zu quantifizieren.

Für eine Reihe diskreter Werte $y_i$ (mit $1 \le i \le N$), die in monoton steigender Reihenfolge vorliegen ($y_i \le y_{i+1}$), ist der Gini-Koeffizient definiert als
$$ G = 2 \frac{\sum_{i=1}^n i \cdot y_i }{n \sum_{i=1}^n y_i} − \frac{n+1}{n}$$
#%% md
3. **\[6 Pt\]**  Schreiben Sie zunächst eine Funktion, welche den Gini-Koeffizienten für die Einkommensverteilung berechnet. Berechnen Sie dann den Gini-Koeffizienten der Einkommensverteilung. Nutzen Sie dafür die Rohdaten sowie die mittels Ihrer zweier unterschiedlichen Strategien bearbeiteten Daten.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
#%% md
## Zusatz (unbewertet)
#%% md
Stellen Sie mehrere Hypothesen auf, wie die Einkommen verteilt sein könnten. Falls Ihnen nichts einfällt, hier ein paar Ideen:

- die Einkommen sind normalverteilt
- das Inverse der Einkommen ist gleichmäßig verteilt
- das Quadrat der Einkommen ist normalverteilt
- der Logarithmus der Einkommen ist gleichmäßig verteilt

Simulieren Sie diese Verteilungen und vergleichen Sie die dazugehörigen Lorenzkurven mit der tatsächlichen Einkommensverteilung. Können Sie eine der Hypothesen bestätigen?
#%%
# Bitte schreiben Sie hier Ihren Programmcode.