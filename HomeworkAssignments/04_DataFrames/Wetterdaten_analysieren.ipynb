#%% md
# Wetterdaten von 1950 bis (fast) heute: Analysieren
#%%
%matplotlib inline

import numpy as np
import pandas as pd
from matplotlib import pyplot as plt
#%% md
## [2 Punkte] Einlesen des bereinigten Datensatzes
#%% md
Lesen Sie den bereinigten Datensatz aus der Datei `produkt_tu_stunde_19500101_20221231_01639_bereinigt.csv` in einen `pd.DataFrame` ein. Dieser Datensatz basiert auf Daten des Deutschen Wetterdienstes, die dieser unter `https://opendata.dwd.de/` bereitstellt, wurde aber bereinigt, damit diese Aufgabe komplett unabhängig von der Aufgabe `Wetterdaten_vorbereiten` bearbeitet werden kann.

Insbesondere sollen Sie die Spalte `Datum` als Index mit Einträgen vom Typ `np.datetime64[ns]` einlesen. Nutzen Sie dazu die [`pd.read_csv`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html)-<PERSON><PERSON>  unter Angabe von `index_col` und `parse_dates=True`.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
# CSV-Datei einlesen mit Datum als Index
df = pd.read_csv('produkt_tu_stunde_19500101_20221231_01639_bereinigt.csv',
                 index_col='Datum',
                 parse_dates=True)

print(df.head())
print(df.info())
#%% md
## [4 Punkte] Probleme der Visualisierung mit Histogrammen und `value_counts`
#%% md
Für die explorative Datenanalyse sind Histogramme und Plots der `value_counts` möglicherweise gut geeignet, aber zur Visualisierung nicht immer. Insbesondere können abhängig von der Datenquelle in Ihrer Darstellung Artefakte auftreten.

Stellen Sie die bereinigten Messwerte der Luftfeuchtigkeit als Histogramm, sowie die `value_counts` der bereinigten Temperaturdaten als Scatterplot dar. Variieren Sie die Anzahl der `bins` des Histogramms. Welche Artefakte fallen Ihnen jeweils auf? Welche Ursachen könnte dies haben?
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
# Bitte schreiben Sie hier Ihren Programmcode.

# Histogramm für Luftfeuchtigkeit mit verschiedenen bins
plt.figure(figsize=(12, 8))

# Verschiedene bin-Anzahlen testen
bin_counts = [10, 50, 100, 200]

for i, bins in enumerate(bin_counts, 1):
    plt.subplot(2, 2, i)
    plt.hist(df['Luftfeuchte'].dropna(), bins=bins, edgecolor='black')
    plt.title(f'Luftfeuchtigkeit - {bins} bins')
    plt.xlabel('Luftfeuchte (%)')
    plt.ylabel('Häufigkeit')

plt.tight_layout()
plt.show()

# Value counts für Temperatur als Scatterplot
plt.figure(figsize=(10, 6))
temp_counts = df['Temperatur'].value_counts().sort_index()
plt.scatter(temp_counts.index, temp_counts.values, alpha=0.5)
plt.title('Häufigkeit der Temperaturwerte')
plt.xlabel('Temperatur (°C)')
plt.ylabel('Anzahl Messungen')
plt.grid(True, alpha=0.3)
plt.show()

# Artefakte beschreiben
print("Beobachtete Artefakte:")
print("1. Luftfeuchtigkeit: Bei wenigen bins sieht man die Verteilung nicht gut.")
print("   Bei vielen bins sieht man Lücken/Sprünge bei bestimmten Werten.")
print("2. Temperatur: Die Werte sind auf 0.1°C gerundet - man sieht diskrete Linien.")
print("   Manche Temperaturen kommen viel häufiger vor (z.B. ganze Zahlen).")
print("Ursache: Die Messgeräte haben begrenzte Genauigkeit und runden auf 0.1°C.")
#%% md
## [6 Punkte] Saisonalität der Temperatur und Luftfeuchtigkeit

Temperatur und Luftfeuchtigkeit sind *saisonale* Größen, sie ändern sich also einigermaßen vorhersagbar im Verlauf eines Jahres (auf der Nordhalbkugel ist es normalerweise im Juni wärmer als im Januar). Diese Saisonalität sollen Sie als erstes visualisieren. Beide Größen schwanken jedoch auch im Tagesverlauf — diese Periodizität interessiert uns hier aber nicht. Überlegen Sie insbesondere, wie Sie dies berücksichtigen.

Stellen Sie für die Monate Januar bis Dezember die *Verteilung* beider Größen über die Jahre hinweg in separaten Plots dar. Auf der Ordinate soll der Monat aufgetragen werden, auf der Abszisse die jeweilige Messgröße. Wählen Sie eine sinnvolle Darstellungsform, die nicht nur Mittelwert und Standardabweichung, sondern die tatsächliche Verteilung der Werte darstellt. Zum Beispiel könnten Sie einen Boxplot (`plt.boxplot`) oder einen Violinplot (`plt.violin`) nutzen.

*Hinweis*: Die `pd.DataFrame.groupby`-Methode könnte sich als nützlich erweisen.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
# Bitte schreiben Sie hier Ihren Programmcode.

# Monat aus dem Datum extrahieren
df['Monat'] = df.index.month

# Daten nach Monat gruppieren
temp_by_month = df.groupby('Monat')['Temperatur'].apply(list)
feuchte_by_month = df.groupby('Monat')['Luftfeuchte'].apply(list)

# Boxplot für Temperatur
plt.figure(figsize=(12, 6))
plt.subplot(1, 2, 1)

# Listen für Boxplot vorbereiten (NaN-Werte entfernen)
temp_data = []
for monat in range(1, 13):
    monatsdaten = [x for x in temp_by_month[monat] if pd.notna(x)]
    temp_data.append(monatsdaten)

plt.boxplot(temp_data, labels=['Jan', 'Feb', 'Mär', 'Apr', 'Mai', 'Jun',
                               'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez'])
plt.title('Temperaturverteilung nach Monat')
plt.xlabel('Monat')
plt.ylabel('Temperatur (°C)')
plt.grid(True, alpha=0.3)

# Boxplot für Luftfeuchtigkeit
plt.subplot(1, 2, 2)

# Listen für Boxplot vorbereiten (NaN-Werte entfernen)
feuchte_data = []
for monat in range(1, 13):
    monatsdaten = [x for x in feuchte_by_month[monat] if pd.notna(x)]
    feuchte_data.append(monatsdaten)

plt.boxplot(feuchte_data, labels=['Jan', 'Feb', 'Mär', 'Apr', 'Mai', 'Jun',
                                  'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez'])
plt.title('Luftfeuchtigkeitsverteilung nach Monat')
plt.xlabel('Monat')
plt.ylabel('Luftfeuchte (%)')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Monatsspalte wieder entfernen
df.drop('Monat', axis=1, inplace=True)
#%% md
## [8 Punkte] Langfristige Veränderung der Jahrestemperatur

Infolge der Erhitzung der Erde in den letzten Jahrzehnten, die in weiten Teilen der Welt bereits katastrophale Auswirkungen zeigt, hat sich auch das Klima in Gießen verändert. Stellen Sie diese Veränderung grafisch dar. Dabei ist die genaue Art der Darstellung Ihnen überlassen. Lassen Sie sich gerne z.B. von [anderen Darstellungen](https://en.wikipedia.org/wiki/Climate_change_art) inspirieren.
#%%
# Bitte schreiben Sie hier Ihren Programmcode.
# Bitte schreiben Sie hier Ihren Programmcode.

# Jahresmitteltemperatur berechnen
jahres_temp = df.groupby(df.index.year)['Temperatur'].mean()

# Einfache Visualisierung der Erwärmung
plt.figure(figsize=(12, 8))

# Plot 1: Zeitreihe mit Trend
plt.subplot(2, 1, 1)
plt.plot(jahres_temp.index, jahres_temp.values, 'b-', label='Jahresmittel')

# Trendlinie hinzufügen
z = np.polyfit(jahres_temp.index, jahres_temp.values, 1)
p = np.poly1d(z)
plt.plot(jahres_temp.index, p(jahres_temp.index), 'r--', linewidth=2, label='Trend')

plt.title('Entwicklung der Jahresmitteltemperatur in Gießen')
plt.xlabel('Jahr')
plt.ylabel('Temperatur (°C)')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 2: Temperatur-Streifen (warming stripes)
plt.subplot(2, 1, 2)

# Temperaturabweichung vom Mittel berechnen
mittelwert = jahres_temp.mean()
abweichung = jahres_temp - mittelwert

# Farbcodierung: blau = kälter, rot = wärmer
farben = plt.cm.RdBu_r((abweichung - abweichung.min()) / (abweichung.max() - abweichung.min()))

# Streifen zeichnen
for i, (jahr, farbe) in enumerate(zip(jahres_temp.index, farben)):
    plt.bar(jahr, 1, width=1, color=farbe, edgecolor='none')

plt.title('Temperaturstreifen: Abweichung vom Mittelwert (blau=kälter, rot=wärmer)')
plt.xlabel('Jahr')
plt.yticks([])
plt.xlim(jahres_temp.index.min() - 0.5, jahres_temp.index.max() + 0.5)

plt.tight_layout()
plt.show()

# Statistik ausgeben
print(f"Durchschnittstemperatur 1950-1970: {jahres_temp[1950:1971].mean():.2f}°C")
print(f"Durchschnittstemperatur 2000-2022: {jahres_temp[2000:2023].mean():.2f}°C")
print(f"Erwärmung: {jahres_temp[2000:2023].mean() - jahres_temp[1950:1971].mean():.2f}°C")
#%% md
 