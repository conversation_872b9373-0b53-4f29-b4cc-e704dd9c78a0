#%% md
# Wetterdaten von 1950 bis (fast) heute: Vorbereiten

#%%
%matplotlib inline

import io
import pathlib
import urllib
import zipfile

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
#%% md
In dieser Aufgabe verarbeiten, bereinigen und visualisieren Sie Wetterdaten des Deutschen Wetterdienstes. Der Deutsche Wetterdienst stellt für seine zahlreichen Wetterstationen `zip`-Archive mit ihren Messdaten bereit. Dabei gibt es Archive für verschiedene Häufigkeiten (von zehnminütlich bis zum zehnjährigen Mittel), verschiedene Messgrößen (etwa Sonneneinstrahlung oder Wind), sowie historische oder aktuelle Werte. In dieser Aufgabe interessieren wir uns für die historischen Werte (von 1950 bis 2023) der Temperatur und Luftfeuchtigkeit der Station "Gießen/Wettenberg".

#%% md
Für eine vollständig bepunktete Abgabe sollten Sie bei der Umsetzung insbesondere auf idiomatischen, performanten, und gut lesbaren Code achten:

- Nutzen Sie `pandas`-Methoden, statt explizit zu iterieren.
- Nutzen Sie Method Chaining, statt Rechnungen zu wiederholen oder unnötige Kopien zu erstellen.
- Formatieren Sie Ihren Code sinnvoll, nutzen Sie "sprechende" Variablen und Methoden wo möglich, und kommentieren Sie wo nötig.

#%% md
## [1 Punkte] Download von den Seiten des Deutschen Wetterdienstes

#%% md
Die Funktion `extract_zipfile_from_web` lädt eine `.zip`-Datei herunter und extrahiert sie in ein Verzeichnis (standardmäßig `tmp`). Sie gibt eine Liste der extrahierten Dateien zurück.

#%%
def extract_zipfile_from_web(url: str, tmp_dir=pathlib.Path('tmp'), exist_ok=True):
    """Extracts a zipfile from the web to `tmp_dir` and returns list of extracted files.

    url      -- location of the zipfile to download from the web
    tmp_dir  -- output directory for extracted files
    exist_ok -- whether to extract to existing directories

    returns list of paths with extracted files
    """
    tmp_dir.mkdir(exist_ok=exist_ok)

    response = urllib.request.urlopen(url)
    z = zipfile.ZipFile(io.BytesIO(response.read()))

    z.extractall(path=tmp_dir)
    return list(map(pathlib.Path(tmp_dir).joinpath, z.namelist()))
#%% md
Das gewünschte Archiv (historische, stündliche Messwerte der Temperatur und Luftfeuchte in Gießen/Wettenberg) findet sich unter der folgenden URL:

#%%
url = (
    'https://opendata.dwd.de/climate_environment/CDC/observations_germany/climate/hourly/'
    'air_temperature/historical/stundenwerte_TU_01639_19500101_20241231_hist.zip'
)
#%% md
Laden Sie es zunächst herunter und entpacken Sie es:

#%%
extracted_files = extract_zipfile_from_web(url)
extracted_files
#%% md
## [3 Punkte] Import des Datensatzes

#%% md
Das Zip-Archiv enthält einige Dateien mit dem Präfix `Metadaten_`, die Informationen u.A. zu den Messgeräten und zur Geographie enthalten. Die verbleibende Datei mit dem Präfix `produkt_` enthält die tatsächlichen Messwerte. Dabei handelt es sich um eine CSV-artige Datei (lassen Sie nicht von der Endung `.txt` irritieren). Die einzelnen Spalten dieser Datei haben folgende Titel:

```
STATIONS_ID;MESS_DATUM;QN_9;TT_TU;RF_TU;eor
```

`MESS_DATUM` enthält das Messdatum, `TT_TU` die Temperatur in Grad Celsius, und `RF_TU` die relative Luftfeuchtigkeit.

#%% md
Lesen Sie (ausschließlich) die Spalten `"MESS_DATUM"`, `"TT_TU"` und `"RF_TU"` in einen `DataFrame` ein. Verzichten Sie zunächst auf die Konversion von Datentypen und das Umbenennen von Spalten.

#%%
# Bitte schreiben Sie hier Ihren Programmcode.
df = pd.read_csv('tmp/produkt_tu_stunde_19500101_20241231_01639.txt', sep=';', usecols=['MESS_DATUM', 'TT_TU', 'RF_TU'])
df.head()
#%% md
## [6 Punkte] Datensatz aufbereiten

#%% md
Nach dem Einlesen eines Datensatzes muss dieser häufig aufbereitet werden. Dafür bietet es sich an, ein "Rezept" zu schreiben, dessen einzelne Schritte Sie dann nacheinander implementieren können. Hier verwenden wir dieses "Rezept":

1. Datentypen umwandeln
2. Spalten umbenennen
3. Index wählen

#%% md
#### Datentypen umwandeln

Die Spalte `MESS_DATUM` enthält das Datum einer jeden Messung. Allerdings hat die Spalte nach Import den Typ `int64` und soll ein Datum im Format `%Y%m%d%H` ("JahrMonatTagStunde") repräsentieren. Das ist nicht in unserem Sinne. Stattdessen würden wir das Datum der Messung lieber mit dem Datentyp `datetime64[ns]` speichern. Damit lassen sich die Daten besser verarbeiten und visualisieren.

Dazu könnten Sie die `.astype`, `.to_datetime`, und `.assign`-Methoden nutzen. Beispielsweise lässt sich eine Series von Jahreszahlen als Integer wie folgt in den Typ `datetime64` umwandeln:

```python
>>> years = pd.Series([2019, 2020])
>>> pd.to_datetime(years.astype(str), format='%Y')
pd.Series(['2019-01-01', '2020-01-01'], dtype=datetime64[ns])
```

Wählen Sie außerdem geeignete Datentypen für Temperatur und Luftfeuchtigkeit.

#%% md
#### Spalten umbenennen

Benennen Sie die Spalten `MESS_DATUM`, `TT_TU`, und `RF_TU` respektive in `Datum`, `Temperatur`, und `Luftfeuchte` um. Dazu bietet sich die `.rename`-Methode an.

#%% md
#### Index wählen

Beim Einlesen der Daten hat pandas einen neuen Integer-Index erzeugt. Stattdessen wollen wir das `Datum` als Index wählen. Nutzen Sie dazu die `.set_index`-Methode.

#%%
# Bitte schreiben Sie hier Ihren Programmcode.
df = (df
      .assign(MESS_DATUM=lambda x: pd.to_datetime(x['MESS_DATUM'].astype(str),
                                                   format='%Y%m%d%H'))
      .rename(columns={
          'MESS_DATUM': 'Datum',
          'TT_TU': 'Temperatur',
          'RF_TU': 'Luftfeuchte'
      })
      .set_index('Datum')
)
df.head()
#%% md
## [10 Punkte] Datensatz bereinigen

Bei Messungen über lange Zeiträume hinweg kann es durchaus sein, dass einzelne Messungen fehlgeschlagen sind, etwa weil ein Messgerät eine Fehlfunktion hat oder gewartet wird. Zum Teil stehen dann im Datensatz unsinnige Messwerte. Solche Daten müssen detektiert und aus dem Datensatz entfernt werden — ein Problem, das auch allgemein bei großen Datenmengen auftreten kann.

Ein "Rezept" zum Finden unsinniger Messwerten kann folgende Schritte beinhalten:

- Überlegen Sie sich zunächst, welchen Wertebereich Sie (grob) erwarten.
- Prüfen Sie, ob Ihr Datensatz bereits fehlende Werte enthält
- Wiederholen Sie dann die folgenden Schritte, bis Sie keine unsinnigen Werte mehr finden:
  1. Prüfen Sie (besonders) Maximum, Minimum, und Median mit der `.describe`-Methode.
  2. Erstellen Sie ein Histogramm für jede Messgröße.
  3. Suchen Sie in den `.value_counts` nach Werten, die besonders häufig oder besonders selten vorkommen.
  4. Ersetzen Sie alle unsinnigen Messwerte mit einem fehlenden Wert (je nach Datentyp `np.nan`, `pd.NA`, `np.datetime64('NaT')` oder `None`)

Wenn Werte fehlen oder entfernt werden mussten, können zum Umgang damit mehrere Strategien sinnvoll sein:

- im Datensatz belassen
- aus dem Datensatz entfernen
- interpolieren, z.B. linear, mit Splines, oder dem nächsten bekannten Wert
- mit einem sinnvollen Wert ersetzen, z.B. Mittelwert oder Median

#%% md
Überprüfen Sie auf geeignete Weise, ob und welche Messwerte für Temperatur und/oder Luftfeuchtigkeit in einem nicht sinnvollen Bereich liegen, und entfernen Sie diese. Achten Sie darauf, keine unnötigen Kopien der Daten zu erzeugen, sondern den existierenden `DataFrame` geeignet anzupassen. Dokumentieren Sie Ihr Vorgehen angemessen.

#%%
# Bitte schreiben Sie hier Ihren Programmcode.
print("Statistische Übersicht:")
print(df.describe())

# 2. Fehlende Werte prüfen
print("\nFehlende Werte vor Bereinigung:")
print(df.isnull().sum())

# 3. Histogramme erstellen
fig, axes = plt.subplots(1, 2, figsize=(12, 5))

# Temperatur Histogramm
axes[0].hist(df['Temperatur'].dropna(), bins=50, edgecolor='black')
axes[0].set_title('Temperaturverteilung')
axes[0].set_xlabel('Temperatur (°C)')
axes[0].set_ylabel('Häufigkeit')

# Luftfeuchte Histogramm
axes[1].hist(df['Luftfeuchte'].dropna(), bins=50, edgecolor='black')
axes[1].set_title('Luftfeuchteverteilung')
axes[1].set_xlabel('Luftfeuchte (%)')
axes[1].set_ylabel('Häufigkeit')

plt.tight_layout()
plt.show()


print("\nHäufigste Temperaturwerte:")
print(df['Temperatur'].value_counts().head(10))

print("\nSeltenste Temperaturwerte:")
print(df['Temperatur'].value_counts().tail(10))

print("\nHäufigste Luftfeuchtigkeitswerte:")
print(df['Luftfeuchte'].value_counts().head(10))

# Temperatur bereinigen
temp_mask = (df['Temperatur'] < -20) | (df['Temperatur'] > 35)
print(f"\nAnzahl unsinniger Temperaturwerte: {temp_mask.sum()}")
df.loc[temp_mask, 'Temperatur'] = np.nan

# Luftfeuchte bereinigen
humidity_mask = (df['Luftfeuchte'] < 0) | (df['Luftfeuchte'] > 100)
print(f"Anzahl unsinniger Luftfeuchtigkeitswerte: {humidity_mask.sum()}")
df.loc[humidity_mask, 'Luftfeuchte'] = np.nan

#  -999 als Fehlwert-Indikator
special_value_temp = (df['Temperatur'] == -999).sum()
special_value_humidity = (df['Luftfeuchte'] == -999).sum()

if special_value_temp > 0:
    print(f"\nGefundene -999 Werte in Temperatur: {special_value_temp}")
    df.loc[df['Temperatur'] == -999, 'Temperatur'] = np.nan

if special_value_humidity > 0:
    print(f"Gefundene -999 Werte in Luftfeuchte: {special_value_humidity}")
    df.loc[df['Luftfeuchte'] == -999, 'Luftfeuchte'] = np.nan

print("Fehlende Werte:")
print(df.isnull().sum())

print("\nNeue Statistik:")
print(df.describe())
#%% md
 