#%% md
## Introduction

#%% md
### What about arrays?

#%% md
#### Why we need arrays?

#%% md
- $N$-dimensional arrays of numerical data are essential for data analysis and scientific computing
  - Natural sciences & numerical mathematics
    - Vectors, matrices, tensors
  - Data Science
    - Datasets (e.g. via Pandas), tensors

#%% md
- Linear algebra
  - Matrix-vector multiplication, matrix-matrix multiplication
- Statistics with large datasets
  - Aggregating data for computing mean, standard deviation, ...
- Deep learning:
  - Operations involving high-dimensional arrays ("tensors")

#%% md
#### Why not use Python's builtin container types (`list`, `tuple`)?

#%% md
- Can hold _any type_ of Python object
  - (Mostly?) Not suitable for native CPU instructions
  - Agnostic of concept of e.g. a rectangular array
- Not designed with numerical calculations in mind

Not efficient enough to be used for "number crunching".

#%% md
- native Python does not provide an efficient and convenient realization of $N$-dimensional arrays of numerical data.

* Efficient libraries for arrays exist in C/C++ and Fortran
  - BLAS and LAPACK
  - FFTW
* We want to access these libraries from Python!
  - Python provides "ease of use"
  - compiled programming languages provide efficient, speedy numerics

#%% md
#### NumPy

#%%

#%% md
##### Why learn Numpy?

#%% md
NumPy is the foundation of the Scientific Python stack. Key features of NumPy include:

- `ndarray` type: N-dimensional arrays with compact and efficient data representation in memory
- NumPy `ndarray`s are multi-dimensional (, fixed-size) containers of items of the same _size_ and _type_
- convenient interface for programmers to access/modify data and metadata
  - dimension
  - shape
  - numerical data type

#%% md
Numpy is used for:

- basic scientific computing routines:
  - matrix-matrix multiplication
  - fourier transform
  - basic statistics
- common input/output interface and "inner workings" of many scientific Python libraries
  - SciPy
  - Pandas
  - Matplotlib

#%% md
##### Speed comparison

#%% md
`np.ndarray`s are _much_ faster than Python built-in containers for numeric calculations

#%% md
Make list of squares (we will see details for creation and working with numpy arrays later on):

#%%

#%%

#%% md
Now, let's test the time for creation of such a list/array:

#%%

#%% md
Now, we can also test the time for summation of a list of squares:

#%%

#%% md
First, consistency check:

#%%

#%% md
Time:

#%%

#%%

#%% md
This is a strange result ...

#%% md
... let's try another way, a sum within numpy:

#%%

#%%

#%% md
This is way more faster. Thus, it seems that we have to pay attention, which methods we are using when dealing with numpy arrays.

#%% md
**Conclusion:** arithmetics with numpy arrays can be 10 to 1000 times faster than using `list`or `tuple`.
