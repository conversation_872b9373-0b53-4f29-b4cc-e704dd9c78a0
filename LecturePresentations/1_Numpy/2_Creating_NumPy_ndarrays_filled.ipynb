#%%
import numpy as np
#%% md
# Creating NumPy [`ndarray`s](https://numpy.org/doc/stable/reference/arrays.ndarray.html)

#%% md
NumPy `ndarray`s are multi-dimensional (, fixed-size) containers of items of the same _size_ and _type_. They...

- can be created from Python containers
- store useful metadata (shape, dtype, memory layout)
- can also be created efficiently by passing a shape
- provide a large speed-up over built-in python

#%% md
## NumPy data types ([dtype](https://numpy.org/doc/stable/reference/arrays.dtypes.html))

#%% md
Why do we have to care about `dtype`?

#%%
(np.arange(10_000) ** 2).sum()
#%% md
Each `np.ndarray` has a data type (`dtype`) for its elements.

- there are different numeric `dtype`s
- can be set, read, and typecast
- this has implications for memory usage
- unlike Python, we might see _overflows_

#%% md
### Review: python built-in numerics

#%% md
Python has built-in numeric datatypes, e.g.:

- integers (`int`)
- floating point numbers (`float`)

#%% md
#### `int`: integer numbers

#%% md
- literals: `42`, `1`, `-15`
- no hard maximum value (e.g. `10**1000` is perfectly valid)
- dynamic size, memory overhead, cannot directly use native CPU instructions for basic math
- $\Rightarrow$ very flexible, but often not very efficient
- typical integer datatypes in C: `int`, `unsigned int`, `long int`

#%% md
#### `float`: floating point numbers

#%% md
- floating point number: $\textrm{sign} * \textrm{mantissa} * \textrm{base}^\textrm{exponent}$, e.g. $-1.234\cdot10^{2}$
- literals in Python: `0.0`, `314.15`, `-1.5e7` (meaning $-1.5\cdot10^{7}$)
- usually implemented as `double` in C (64 bit / 8 byte)
- thus, limited max, min, eps (see [`sys.float_info`](https://docs.python.org/3/library/sys.html#sys.float_info))

#%% md
### NumPy numeric dtypes

#%% md
NumPy has _numeric_ types for...

- `bool`eans
- `int`egers (signed or unsigned)
- `float`s

A (more) complete list of supported data types for `ndarray`s can be found [here](https://numpy.org/doc/stable/user/basics.types.html). The tables also feature the corresponding C datatype. _Note_: There are _platform dependent_ datatypes (e.g. `np.int_`) where the corresponding C type (e.g. `int`) is also platform dependent.

#%% md
Limits of `int32`:

#%%
np.iinfo('int32')
#%% md
Sum with `int64`:

#%%
(np.arange(10_000, dtype=np.int64) ** 2).sum()
#%% md
Sum with `int32`:

#%%
(np.arange(10_000, dtype=np.int32) ** 2).sum()
#%% md
### Setting dtypes

#%% md
Numpy will "do its best" to use a fitting `dtype`.

- deduces the type of the elements from the input
- will typecast to a fitting `dtype`
- but the `dtype` can also be passed explicitly
  - during construction
  - explicit typecasting with `.astype`

#%% md
### Attention: silent Overflow

#%%
np.power(100, 8, dtype=np.int8)
np.power(100, 8, dtype=np.int32)
np.power(100, 8, dtype=np.int64)
np.power(100, 8)
#%% md
## Creation from Python's built-in container types

#%% md
`ndarray`s can easily be constructed from Python lists or tuples

- `set` and `dict` don't work well
- generators don't work at all!
  - `ndarray`s need their size at **creation time**
  - can use `np.fromiter()` to convert them (needs `dtype`)
- _nested_ lists or tuples create multi-dimensional arrays
  - all sublists must have the same size!

#%%
np.array([1, 2, 3])
#%%
np.array((1, 2, 3))
#%%
np.array(i**2 for i in range(5))
#%%
np.fromiter((i**2 for i in range(5)), dtype=int)
#%% md
Nested lists:

#%%
nested_list = [[1, 2, 3, 4], [10, 20, 30, 40]]
np.array(nested_list)
#%%
nested_list = [[1, 2, 3, 4], [10, 20, 30]]
np.array(nested_list)
#%% md
## Accessing metadata

#%% md
arrays store their data, but also their _metadata_ as attributes:

- `shape`: number of dimensions and items
  - N-tuple with non-negative integer values describing the sizes of each dimension
- `dtype`: type of all items
- `itemsize`: memory size per element
- `data`: pointer to start of actual values
- `flags`: `dict` of various settings (e.g. mutability)

#%%
e = np.array([[1.0] * 5, [4] * 5])

print(
    f"""
    {e = },
    {e.shape = },
    {e.size =},
    {e.dtype = },
    {e.itemsize = }
    """
)
#%% md
**Setting data types**

#%%
a = np.array([1, 2, 3])
f"{a = },    {a.dtype = }"
#%%
b = np.array([0.1, 0.55, 9.9])
f"{b = },    {b.dtype = }"
#%%
c = a + b
f"{c = },    {c.dtype = }"
#%%
a = np.array([100**8, 2, 3])
f"{a = },    {a.dtype = }"
#%%
a = np.array([1.0, 2, 3])
f"{a = },    {a.dtype = }"
#%%
b
#%%
b.astype(np.int64)
#%%
b.astype(np.bool_)
#%%
b.astype(np.int64).astype(np.bool_)
#%%
a = np.array([100**8, 2, 3], dtype=np.float32)
f"(a = ),    {a.dtype = }"
#%%
a = np.array([100**8, 2, 3])
a, a.astype(np.float32)
#%% md
## Creation from NumPy built-in functions (factory functions)

#%% md
Numpy provides some factory functions for generating arrays:

- range-like: give a `start` and a `stop`, like Python `range`
- by shape: pass a shape (dimensions), may fill with different values
- copy the shape from an existing array

#%% md
### Range-like array construction

#%% md
Python `range` has Integer `start`, `stop`, and `step`. Numpy has range-inspired factory functions:

- `np.arange`: array with start, end, and step (like python `range`)
- `np.linspace`: span `num` points evenly between `start` and `stop`
- `np.logspace`: likewise, but logarithmically between powers of 10

#%%
np.arange(10)
#%%
np.arange(3, 11, 2)
#%%
np.linspace(start=0, stop=100, num=10)
#%%
np.linspace(start=0, stop=100, num=10, endpoint=False)
#%%
help(np.logspace)
#%% md
### Shape-based array construction

#%% md
- `np.zeros`: array with zeros of given `shape` (and `dtype`)
- `np.ones`: likewise, filled with ones
- `np.full`: likewise, filled with arbitrary value
- `np.eye`: $n\times n$ matrix filled with ones on the "diagonals", zero otherwise.
- `np.diag`: Fill a square 2D array with given values on the diagonal

#%%
np.zeros((2, 3)), np.ones((3, 2))
#%%
np.full((2, 2), fill_value=3)
#%%
np.eye(3)
#%%
np.diag(np.arange(1, 4), k=-1)
#%% md
### Copy shape from existing arrays

#%% md
These all need an input array to take its shape

- `np.zeros_like`: initialize with zeros
- `np.ones_like`: initialize with ones
- `np.full_like`: initialize with arbitrary value

#%%
one = np.ones((3, 3))

np.zeros_like(one), np.full_like(one, fill_value=5)
#%% md
## Tasks

#%% md
#### **1.** Create an `ndarray` named `a` with all odd integers between 0 and 10, in order.

#%%
np.arange(1, 10, 2)
#%%
np.array([1, 3, 5, 7, 9])
#%%
np.array([2 * i + 1 for i in range(5)])
#%%
np.arange(5) * 2 + 1
#%%
np.linspace(1, 9, 5, dtype=int)
#%% md
#### **2.** Create an `ndarray` named `i_5` with the $5\times5$ unit matrix.

#%%
np.eye(5)
#%%

#%% md
#### **3.** Create an `ndarray` named `g` with a $5\times5$ grid spanning the points of a unit square (with corners at `[0, 0], [0, 1], [1, 0], [1, 1]`)

#%%
np.linspace(np.linspace([0, 0], [1, 0], 5), np.linspace([0, 1], [1, 1], 5), 5)
#%%

#%% md
#### **4.** Write a function `hilbert` that creates the Hilbert matrix $H_{ij} = \frac{1}{i+j-1}$ in `n` dimensions.

#%%
def hilbert(n):
    hil = np.array(
        [[f"{i}, {j}" for i in range(1, n + 1)] for j in range(1, n + 1)]
    )

    return hil

#%%
hilbert(5)
#%%

#%%
n = 5
i, j = np.indices((5, 5))

1 / (i + j + 1)