#%%
import numpy as np
#%% md
# Array operations

#%% md
**Array-oriented programming** is a paradigm that seeks to benefit from vectorization, broadcasting, and direct operations on arrays.

- use `numpy` built-in functions and methods of `ndarray` class
- _avoid_ using standard Python loop contructs ("raw" for loops, list comprehension)

#%% md
## Element-wise operations

#%% md
`ndarray`s redefine methods for arithmetics known from basic Python. Operations are performed element-wise and with broadcasting (we will discuss later) to allow arithmetics on arrays of various dimensions.

#%% md
### Arithmetics

#%% md
The "standard Python" operators may be used with arrays

- performed element-wise
- `a + b` is analogous to `[i + j for i, j in zip(a, b)]`
  - likewise with other operations
- these vectorized operations are _much_ faster

#%%

#%%

#%%

#%%

#%% md
**Reminder:** this behavior is completly different for `list`s

#%%

#%% md
We can do element-wise **relations** as well:

#%%

#%%

#%% md
In principle, we can do a element-wise operation also by an iteration:

#%%

#%% md
But looking at times:

#%%

#%% md
### General elementwise operations: `ufunc`s

#%% md
Universal functions (ufuncs) perform function operations on individual elements of `ndarray`s in an element-by-element manner.

- have broadcasting built-in (see later)
- can be passed additional keyword arguments to specify `dtype`s, typecasting, memory order, or axes
- create a new (temporary) target array
  - can use `out=` parameter to write to an existing array
- see the [documentation](https://numpy.org/doc/stable/reference/ufuncs.html) for details.

#%% md
**List of `ufunc`s provided by numpy**

#%% md
There are more than 60 universal functions. Some overload builtin Python operators.

- arithmetics (as seen above)
  - `np.add`, `.subtract`, `.multiply`, etc. overload `+`, `-`, `*`, etc.
- comparison
  - `np.equals`, `np.greater`, etc. overload `==`, `>`, etc.
- bitwise operators
  - `np.bitwise_and`, `np.bitwise_or`, `np.bitwise_xor`, `np.bitwise_not` overload `&`, `|`, `^`, `~`
- logical operators
  - `np.logical_and`, `np.logical_or`, `np.logical_xor`, `np.logical_not`
  - important for Boolean masking: `np.logical_and` and `np.bitwise_and` are the same and thus `&`can be used (similar for the other logical operators)

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
Other `ufunc`s perform **mathematical** or **numeric** functions.

- exponential functions
  - `np.exp`, `np.exp2`, `np.expm1`, `np.logaddexp`
  - `np.log`, `np.log2`, `np.log1p`, `np.log10`
- trigonometric functions
  - `np.sin`, `np.arccos`, `np.tanh`, `np.arctanh2`, etc.
- numerics
  - `np.isfinite`, `np.isinf`, `np.isnan`
  - `np.clip`, `np.floor`, `np.ceil`, `np.trunc`
  - `np.around`, `np.rint`
- other common mathematical functions
  - `np.sqrt`, `np.square`, `np.cbrt`, `np.abs`, `np.conj`, `np.angle`

#%%

#%%

#%% md
A useful resource on universal functions can be found [here](https://jakevdp.github.io/PythonDataScienceHandbook/02.03-computation-on-arrays-ufuncs.html).

#%% md
## Broadcasting

#%% md
### Broadcasting: scalars to `ndarray`

#%% md
Operations with scalars can be broadcast over the whole array.

- operation will be applied to each array element with the scalar
- vectorization saves time
  - much faster than generating redundant information first
  - also faster than using redundant information

#%%

#%% md
**Reminder:** different behavior for `list`s:

#%%

#%% md
Also possible for adding a scalar, which makes mathematicaly no sense:

#%%

#%%

#%% md
### Broadcasting: `nd` to `nd`

#%% md
In fact, numpy will attempt to broadcast values of _any_ shape together to "make things fit":

1. The shape of the array with fewer dimensions is padded with ones from the left
2. All dimensions of length one are expanded to match the dimensions of the other array

- if that doesn't work, an error is raised
- even expansion of one dimension from one array and another dimension from the second will work (this is akin to the "outer product")
- we can generate a new axis with slicing and `np.newaxis`

#%% md
![image.png](02.05-broadcasting.png)

Fig: Broadcasting visualization. Image taken from [J. VanderPlas, Python Data Science Handbook](https://jakevdp.github.io/PythonDataScienceHandbook/02.05-computation-on-arrays-broadcasting.html))

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
## Array-wide operations

#%% md
At the top level, numpy provides a few methods that operate on entire arrays.

- the result depends on all elements at once
  - can't be broken down to an element-wise operation
- we may _reduce_ the array to a lower dimension
- a few other operations that don't reduce the dimension also exist

#%% md
### Reductions

#%% md
These will reduce the number of dimensions of an array

- reduction by at least one, or even down to a scalar.
- can be applied to _all_ elements or along a specified axis
- often used reductions:
  - _n_-ary operations (`np.sum`, `np.prod`, `np.all`, `np.any`)
  - statistics (`np.mean`, `np.average`, `np.std`)
  - extreme values (`np.min`, `np.max`, `np.argmin`, `np.argmax`)
- "`nan`-safe" versions exist to deal with `np.nan`
  - `np.nansum`, `np.nanprod`, etc.

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
### Other array-wide operations

#%% md
- `np.cumsum` and `np.cumprod` for cumulative sum and product, respectively
- `np.swapaxes`, `np.sort`, `np.partition` reorder elements (the latter two in-place)

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
### Reductions with `ufuncs`

#%% md
(Only) binary `ufuncs` have methods `.reduce` and `.accumulate`

- `.reduce` provide a scalar result
  - `np.add.reduce` is equivalent to `np.sum`
  - `np.multiply.reduce` is equivalent to `np.prod`
- `.accumulate` leave along the way
  - `np.add.accumulate` is equivalent to `np.cumsum`
  - `np.multiply.accumulate` is equivalent to `np.cumprod`

#%%

#%%

#%% md
## Outer product

#%% md
Binary `ufunc`s also have the `.outer` method

- e.g. `np.multiply.outer` is an _outer product_
- evaluates at each _pair_ of inputs
- useful for constructing tables

#%%

#%%

#%%

#%%

#%% md
## Tasks

#%% md
##### **1.** When we take the sine of all integers from 0 to 100, where do we hit the minimum value and which value is that?

#%%

#%%

#%%

#%%

#%%

#%% md
##### **2.** For machine learning, features often need to be normalized. Normalize the columns of the array `e = np.arange(10).reshape((5, 2))` to have 0 mean and unit variance (standard deviation of 1). (Note: we will learn about `reshape` later)

#%%

#%%

#%%

#%%

#%%

#%%
