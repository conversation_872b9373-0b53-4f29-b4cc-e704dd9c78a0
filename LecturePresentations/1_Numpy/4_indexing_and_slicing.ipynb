#%%
import numpy as np
#%% md
# Indexing and Slicing

#%% md
## Basic Indexing and slicing in 1D

#%% md
For basic indexing of one-dimensional arrays, we already start to see some differences to builtin Python (e.g. `list`) slices.

#%% md
### Access and modification

#%% md
This is like Python lists. Nothing to see here. We may access with positive or negative integers and modify values.

#%%

#%%

#%%

#%% md
### Slicing

#%% md
We can also access elements with slices.

- slice notation functions as in standard Python: `array[start:stop:step]`

  - default values are `start=0`, `stop=len(array)`, `step=1`
  - second colon (`:`) is optional if default `step=1` is used
  - indices start at 0, `stop` is exclusive

- assignment to slices is richer than in Python
  - new value can be scalar

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
**Let's make a prime sieve:**

#%%

#%%

#%% md
Just put in a function (Note: the algorithm can be optimized, which we omit here):

#%%

#%%

#%% md
Now we want to get a list of the prime numbers within the interval:

#%%

#%% md
other way:

#%%

#%%

#%%

#%% md
double check:

#%%

#%% md
### Tasks

#%% md
##### **1.** Create an ndarray named count with integers 1 through 20. Then set all entries with even integers to zero.

#%%

#%%

#%% md
##### **2.** Swap the first and last value of `count`.

#%%

#%% md
##### **3.** Starting from `np.arange(8)`, use slicing to get an array named `odd` with values 1, 3, 5, 7.

#%%

#%%

#%% md
##### **4.** Create an array named `alt_count` with entries `[0, 1, 0, 2, 0, 3]`. Use the functions `np.zeros()`, `np.arange()`, and slicing.

#%%

#%%

#%% md
## Basic indexing and slicing in N dimensions

#%% md
`ndarray`s can natively be accessed across dimensions.

- in Python, we'd have to use nested lists: `b[i][j]`
  - can't slice outer index
  - slow access
- numpy allows (and recommends!) access with (elements from) a _tuple of indices_:
  - syntax: `b[i, j]` or `b[(i, j)]`
  - this is much faster (and nicer) than sequential access

#%% md
### Adressing analogous to 1D

#%% md
- both indices may be integers or slices
- can also set to such slices "richly"
  - with scalars
  - with lower-dimensional arrays

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
### Addressing entire dimensions

#%% md
In n dimensions, we may include some dimensions completely

- get an _entire_ dimension with the empty slice (`:`)
  - selects all elements from the specified axis
- get even more entire dimensions with ellipses (`...`)
  - select all elements from all axes which might otherwise need one `:` each
  - must be unambiguous, e.g. `(..., 1, ...)` is not

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
### Tasks

#%% md
##### **1.** Create a 8x8 array named `border` with zeros in the interior and ones at the "border" elements.

#%%

#%%

#%%

#%% md
##### **2.** Create an 8x8 array named `check` with a checkerboard pattern of 0 and 1.

#%%

#%%

#%%
