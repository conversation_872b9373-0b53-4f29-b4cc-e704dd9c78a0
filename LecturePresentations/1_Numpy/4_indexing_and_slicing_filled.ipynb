#%%
import numpy as np
#%% md
# Indexing and Slicing

#%% md
## Basic Indexing and slicing in 1D

#%% md
For basic indexing of one-dimensional arrays, we already start to see some differences to builtin Python (e.g. `list`) slices.

#%% md
### Access and modification

#%% md
This is like Python lists. Nothing to see here. We may access with positive or negative integers and modify values.

#%%
A = np.array([10, 20, 39])
A[0]
#%%
A[2] = 222
A
#%%
A[-1]
#%% md
### Slicing

#%% md
We can also access elements with slices.

- slice notation functions as in standard Python: `array[start:stop:step]`

  - default values are `start=0`, `stop=len(array)`, `step=1`
  - second colon (`:`) is optional if default `step=1` is used
  - indices start at 0, `stop` is exclusive

- assignment to slices is richer than in Python
  - new value can be scalar

#%%
a = np.arange(10)
a
#%%
a[0:6:1], a[0:6:], a[0:6], a[:6], a[6]
#%%
a[:-2]
#%%
a[1::2]
#%%
a[::-1]
#%%
a[3:1:-1]
#%% md
**Let's make a prime sieve:**

#%%
a = np.ones(20, dtype=bool)
a[0:2] = False
a
#%%
for (idx,), val in np.ndenumerate(a):
    if val:
        a[2 * idx :: idx] = False

a
#%%
for (idx,), val in np.ndenumerate(a):
    print(idx, val)
    if val:
        a[2 * idx:: idx] = False
        print(a)
#%% md
Just put in a function (Note: the algorithm can be optimized, which we omit here):

#%%
def prime_sieve(n):
    a = np.ones(n, dtype=bool)
    a[0:2] = False
    for (idx,), val in np.ndenumerate(a):
        if val:
            a[2 * idx :: idx] = False
    return a

prime_sieve(20)
#%%
size = 100000
sieve = prime_sieve(size)
#%% md
Now we want to get a list of the prime numbers within the interval:

#%%
np.array([idx for idx, x in enumerate(sieve) if x])
#%% md
other way:

#%%
np.nonzero(sieve)
np.where(sieve)
#%%
%timeit np.array([idx for idx, x in enumerate(sieve) if x])
%timeit np.nonzero(sieve)
%timeit np.where(sieve)
#%%
primes = np.where(sieve)
#%% md
double check:

#%%
sieve[primes].all()
#%% md
### Tasks

#%% md
##### **1.** Create an ndarray named count with integers 1 through 20. Then set all entries with even integers to zero.

#%%
count = np.arange(1, 21)
count[1::2] = 0
count
#%%

#%% md
##### **2.** Swap the first and last value of `count`.

#%%
count[-1], count[0] = count[0], count[-1]
count
#%% md
##### **3.** Starting from `np.arange(8)`, use slicing to get an array named `odd` with values 1, 3, 5, 7.

#%%
a = np.arange(8)
a
#%%
odd = a[1::2]
odd
#%% md
##### **4.** Create an array named `alt_count` with entries `[0, 1, 0, 2, 0, 3]`. Use the functions `np.zeros()`, `np.arange()`, and slicing.

#%%
alt_count = np.zeros(6, dtype=np.uint8)
alt_count
#%%
alt_count[1::2] = np.arange(1, 4)
alt_count
#%% md
## Basic indexing and slicing in N dimensions

#%% md
`ndarray`s can natively be accessed across dimensions.

- in Python, we'd have to use nested lists: `b[i][j]`
  - can't slice outer index
  - slow access
- numpy allows (and recommends!) access with (elements from) a _tuple of indices_:
  - syntax: `b[i, j]` or `b[(i, j)]`
  - this is much faster (and nicer) than sequential access

#%% md
### Adressing analogous to 1D

#%% md
- both indices may be integers or slices
- can also set to such slices "richly"
  - with scalars
  - with lower-dimensional arrays

#%%
b = np.array([[1, 2, 3], [10, 20, 30]])
print(f"{b},   {b.shape = }")
#%%
b[(0, 1)], b[0, 1]
#%%
b[:2, :2], b[slice(None, 2), slice(None, 2)]
#%%
b[1, 0::2] = 20
b
#%%
a = np.arange(1, 25).reshape((4, 6))
a
#%%
a[0, 3:-1]
#%%
a[:2, :2] = [1, 2]
a[-2:, -2:] = [[1], 
               [2]]
a
#%% md
### Addressing entire dimensions

#%% md
In n dimensions, we may include some dimensions completely

- get an _entire_ dimension with the empty slice (`:`)
  - selects all elements from the specified axis
- get even more entire dimensions with ellipses (`...`)
  - select all elements from all axes which might otherwise need one `:` each
  - must be unambiguous, e.g. `(..., 1, ...)` is not

#%%
a
#%%
a[:, -1], a[-1, :]
#%%
a[::2, 3::2]
#%%
three_d = a.reshape(3, 2, 4)
three_d
#%%
three_d[..., -1]
#%%
three_d[-1, ..., -1]
#%% md
### Tasks

#%% md
##### **1.** Create a 8x8 array named `border` with zeros in the interior and ones at the "border" elements.

#%%
border = np.ones((8,8))
border
#%%
border[1:-1, 1:-1] = 0
border
#%%
border = np.zeros((8,8))
border[0, :] = 1
border[-1, :] = 1
border[:, 0] = 1
border[:, -1] = 1
border
#%% md
##### **2.** Create an 8x8 array named `check` with a checkerboard pattern of 0 and 1.

#%%
check = np.zeros((8, 8))
check
#%%
check[::2, ::2] = 1
check
#%%
check[1::2, 1::2] = 1
check
#%%
