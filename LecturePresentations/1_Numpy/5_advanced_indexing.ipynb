#%%
import numpy as np
#%% md
# Advanced Indexing

#%% md
## Advanced indexing in 1D

#%% md
- Numpy allows using arrays to index other arrays

  - integer dtype: Select at these indices
  - boolean dtype: Select all where entry is `true`

- also called "fancy indexing", allows finely controlled access
  - when modifying data, operates in-place (like a view)
  - when extracting data, returns a _copy_

#%% md
### Indexing with integers

#%% md
pass an array or list of integers

- of any length (in particular: longer than original array)
- in any order (in particular: repeat indices)
- if we pass an `ndarray`: We'll get an array of the same shape out

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
### Indexing with Boolean masks

#%% md
A "Boolean Mask" for a given one-dimensional array is...

- an array with the same length
- of `dtype` boolean
- typically created with _comparison_ operators between arrays
  - `==`, `!=`, `>`, `<`, `<=`, `>=`
- often processed (or combined) with _bitwise_ operators
  - `&`, `|`, `^`, `~`
- then used for indexing

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
**simpler:**

#%%

#%%

#%%

#%%

#%% md
### Tasks

#%% md
##### **1.** Repeatedly overwrite the array a = np.arange(1, 12) % 11 by indexing it with itself. What happens?

#%%

#%%

#%%

#%% md
##### **2.** Array FizzBuzz. Take `b = np.arange(100)`. If values are dividable by 3, replace them with `-3` ("Fizz"). If they are dividable by 5, replace them with `-5` ("Buzz"). If they are dividable by both, replace with `-35` ("FizzBuzz"). Use Boolean masks.

#%%

#%%

#%%

#%%

#%% md
##### **3.** Array FizzBuzz. Take `b = np.arange(100)`. If values contain the digit 3, replace them with `-3` ("Fizz"). If they contain the digit 5, replace them with `-5` ("Buzz"). If they contain both, replace with `-35` ("FizzBuzz"). Use Boolean masks.

#%%

#%%

#%%

#%%

#%%

#%% md
##### **4.** How many squares of integers between 0 and 100 have "6" as their second-lowest ("tens") digit?

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
## Advanced indexing in more than one dimension

#%% md
- Multiple indices are broadcast together
  - along same indices: Form pairs of indices
  - across different indices: Form all combinations
- we can combine basic and fancy indexing
- we can combine different kinds of fancy indexing

#%%

#%%

#%%

#%%

#%% md
This means it's the elements that are a combination of the arrays rows = [0,3] and cols = [1,2] and thus (0,1) and (3,2):

#%%

#%% md
In order to get the sub-matrix selected by rows and cols there are two options:

#%%

#%% md
Other option with `newaxis`

#%%

#%% md
Hint: this is similar to broadcasting in aritmetics:

#%%

#%%

#%% md
Example for **boolean masks**:

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
Note: the other option with `newaxis`ist not working, because np tries to construct indecis, which are here just (True, False) or (False,False) etc. that cannot be used for indexing.

#%% md
### Tasks

#%% md
##### **1.** What values do `a` and `b` hold after this snippet, and why? What is the "correct" way to do it?

```python
a, b = np.eye(3), np.eye(3)
mask = [True, False, False]
index = 0
a[mask][index] = 2
b[index][mask] = 2
```

#%%
