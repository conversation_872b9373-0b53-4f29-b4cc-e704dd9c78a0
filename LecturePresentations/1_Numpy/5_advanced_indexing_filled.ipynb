#%%
import numpy as np
#%% md
# Advanced Indexing

#%% md
## Advanced indexing in 1D

#%% md
- Numpy allows using arrays to index other arrays

  - integer dtype: Select at these indices
  - boolean dtype: Select all where entry is `true`

- also called "fancy indexing", allows finely controlled access
  - when modifying data, operates in-place (like a view)
  - when extracting data, returns a _copy_

#%% md
### Indexing with integers

#%% md
pass an array or list of integers

- of any length (in particular: longer than original array)
- in any order (in particular: repeat indices)
- if we pass an `ndarray`: We'll get an array of the same shape out

#%%
countdown = np.arange(10, -1, -1)
countdown
#%%
countdown[[0, 3, 8, -1]]
#%%
countdown[[0, 8, -1]] = [100, -10, 30]
countdown
#%%
indices = np.random.randint(0, 10, 50)
indices
#%%
countdown[indices]
#%%
indices = np.array([[0, 1], [5, -1]])
indices
#%%
countdown[indices]
#%%
countdown[[0, 0, 1, 1, 1]] = [1, 2, 3, 4, 5]
countdown
#%% md
### Indexing with Boolean masks

#%% md
A "Boolean Mask" for a given one-dimensional array is...

- an array with the same length
- of `dtype` boolean
- typically created with _comparison_ operators between arrays
  - `==`, `!=`, `>`, `<`, `<=`, `>=`
- often processed (or combined) with _bitwise_ operators
  - `&`, `|`, `^`, `~`
- then used for indexing

#%%
a = np.arange(8)
mask = np.array([True, False] * 4)
a, mask
#%%
a[mask]
#%%
A = np.array([1, 2, 3])
B = np.array([3, 2, 1])
mask = A == B
mask
#%%
A[mask], B[mask]
#%%
squares = np.arange(-10, 10) ** 2
squares
#%%
mask = [square < 20 for square in squares]
mask
#%%
squares[mask]
#%% md
**simpler:**

#%%
squares < 20
#%%
squares[squares < 20]
#%%
4 < squares < 20
#%%
squares[(squares > 4) & (squares < 20)]
(squares > 4) & (squares < 20)
#%% md
### Tasks

#%% md
##### **1.** Repeatedly overwrite the array a = np.arange(1, 12) % 11 by indexing it with itself. What happens?

#%%
a = np.arange(1, 12) % 11
#%%
for i in range(20):
    a = a[a]
    print(a)
#%%

#%% md
##### **2.** Array FizzBuzz. Take `b = np.arange(100)`. If values are dividable by 3, replace them with `-3` ("Fizz"). If they are dividable by 5, replace them with `-5` ("Buzz"). If they are dividable by both, replace with `-35` ("FizzBuzz"). Use Boolean masks.

#%%
b = np.arange(100)
#%%
mask3 = b % 3 == 0
mask5 = b % 5 == 0
#%%
b[mask3] = -3
b[mask5] = -5
#%%
b[mask3 & mask5] = -35
b
#%%

#%% md
##### **3.** Array FizzBuzz. Take `b = np.arange(100)`. If values contain the digit 3, replace them with `-3` ("Fizz"). If they contain the digit 5, replace them with `-5` ("Buzz"). If they contain both, replace with `-35` ("FizzBuzz"). Use Boolean masks.

#%%

b = np.arange(100)
mask3 = np.array(['3' in str(number) for number in b])
mask3
#%%

#%%

#%%

#%%

#%% md
##### **4.** How many squares of integers between 0 and 100 have "6" as their second-lowest ("tens") digit?

#%%
squares = np.arange(0, 101) ** 2
#%%
squares
#%%
squares_red = squares % 100
#%%
mask = (squares_red > 59) & (squares_red < 70)
mask
#%%
squares[mask]
#%%

#%%

#%% md
## Advanced indexing in more than one dimension

#%% md
- Multiple indices are broadcast together
  - along same indices: Form pairs of indices
  - across different indices: Form all combinations
- we can combine basic and fancy indexing
- we can combine different kinds of fancy indexing

#%%
a = np.arange(1, 25).reshape(4, 6)
a
#%%
rows = np.array([0, 3])
a[rows]
#%%
cols = np.array([1, 2])
a[:, cols]
#%%
a[rows, cols]
#%% md
This means it's the elements that are a combination of the arrays rows = [0,3] and cols = [1,2] and thus (0,1) and (3,2):

#%%
a[0, 1], a[3, 2]
#%% md
In order to get the sub-matrix selected by rows and cols there are two options:

#%%
a[rows, :][:, cols]
#%% md
Other option with `newaxis`

#%%
cols, cols[:, np.newaxis]
#%% md
Hint: this is similar to broadcasting in aritmetics:

#%%
rows + cols, rows[:, np.newaxis] + cols
#%%
a[rows[:, np.newaxis], cols]
#%% md
Example for **boolean masks**:

#%%
a
#%%
rows_mask, cols_mask = np.all(a % 10, axis=1), np.all(a % 10, axis=0)
rows_mask, cols_mask
#%%
a[rows_mask, :]
#%%
a[:, cols_mask]
#%%
a[rows_mask, cols_mask]
#%%
a[rows_mask, :][:, cols_mask], a[np.ix_(rows_mask, cols_mask)]
#%%
np.ix_(rows_mask, cols_mask)
#%% md
Note: the other option with `newaxis`ist not working, because np tries to construct indecis, which are here just (True, False) or (False,False) etc. that cannot be used for indexing.

#%% md
### Tasks

#%% md
##### **1.** What values do `a` and `b` hold after this snippet, and why? What is the "correct" way to do it?

```python
a, b = np.eye(3), np.eye(3)
mask = [True, False, False]
index = 0
a[mask][index] = 2
b[index][mask] = 2
```

#%%
a, b = np.eye(3), np.eye(3)
mask = [True, False, False]
index = 0
a[mask][index] = 2
b[index][mask] = 2
#%%
a, b
#%%
