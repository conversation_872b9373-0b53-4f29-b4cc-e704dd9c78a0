#%%
import numpy as np
#%% md
# Views and Copies

#%% md
`ndarray`s are _mutable_. Numpy saves memory by creating _references_ to original data upon slicing or transposing. This has important implications.

- slicing or transposing creates a **view** and _not_ a copy of the original array
  - only creates new metadata, points to the same data
  - we can overwrite data in the original array!
- <PERSON><PERSON><PERSON> can check whether two arrays share memory
  - `np.may_share_memory()` uses heuristics and may give false positives
  - `np.shares_memory()` is exact but "may be exponentially slow"
- we can create actual copies with the `.copy()` method
- when slices of existing arrays are modified, data is copied as well

#%%

#%%

#%%

#%%

#%%

#%% md
Now with copy:

#%%

#%%

#%%

#%%

#%% md
Now with **slicing:**

#%%

#%%

#%%

#%%

#%%

#%% md
With copy:

#%%

#%%

#%% md
However:

#%%

#%%

#%%

#%%

#%% md
### Tasks

#%% md
##### **1.** Create an array with integers from 0 to 10. Slice the `even` and `odd` integers into such-named arrays. Check if these arrays `may_share_memory`, and if they actually do.

#%%

#%% md
##### **2.** Carsten wants to symmetrize his tensor `c` of rank 3 as follows:

```python
c = np.arange(8).reshape((2, 2, 2))
first_permutation = np.transpose(c, axes=(1, 2, 0))
second_permutation = np.transpose(c, axes=(2, 0, 1))
c += first_permutation
c += second_permutation
```

The result is not what he expected. Why not and what can he improve?

#%%

#%%
