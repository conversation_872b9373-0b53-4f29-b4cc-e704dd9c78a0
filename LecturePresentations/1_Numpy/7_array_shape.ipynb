#%%
import numpy as np
#%% md
# Array shape

#%% md
To deal with large amounts of data efficiently, numpy stores data and metadata separately.

- actual values are stored contiguously (if possible)
  - `.data` points to a memory location where data block starts
- metadata explains how the data shall be interpreted
  - see above for explanation on `.shape`, `.dtype`, `.flags`
  - these can be manipulated _independently_ of the data

#%% md
## Shape manipulation

#%% md
We can manipulate the shape of arrays to change how the data represents arrays of different dimensions.

- `.shape` is stored as a tuple: number of entries in each dimension
- `.reshape` is a method that casts arrays to a given shape
- `.T` is an attribute (!) that holds the transformed version of a 2D array
- `np.newaxis`creates a new axis and thus change shape
- reshaped (but not regenerated!) arrays may share memory to hold the data
  - can have weird effects not unlike (mutable) lists

#%% md
### Reshaping

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
### Transpose

#%%

#%%

#%% md
### newaxis

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
## Combination of numpy arrays

#%% md
- arrays can be combined together

#%% md
### Concatenate

#%% md
- _concatenate_ to append to existing dimensions

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
### Stack

#%% md
- _stack_ to generate a new dimension

#%%

#%%

#%%

#%%

#%%

#%%

#%% md
### Split

#%% md
- _split_ to separate into subarrays

#%%

#%%

#%% md
## Tasks

#%% md
##### **1.** Create an ndarray named tens of integers from 0 to 99, grouped by their "tens" digit into a 10×10 -matrix.

#%%

#%% md
##### **2.** Create an ndarray named cube labelling the vertices of a unit cube with integers from 0 to 7, by reshapeing an arange. Transpose that array. How does it change?

#%%

#%%

#%% md
##### **3.** How many different arrays can you generate by concatenating or stacking two np.eye(2) unit matrices?

#%%

#%%

#%%

#%%

#%%

#%%

#%%
