#%%
import numpy as np
#%% md
# Array shape

#%% md
To deal with large amounts of data efficiently, numpy stores data and metadata separately.

- actual values are stored contiguously (if possible)
  - `.data` points to a memory location where data block starts
- metadata explains how the data shall be interpreted
  - see above for explanation on `.shape`, `.dtype`, `.flags`
  - these can be manipulated _independently_ of the data

#%% md
## Shape manipulation

#%% md
We can manipulate the shape of arrays to change how the data represents arrays of different dimensions.

- `.shape` is stored as a tuple: number of entries in each dimension
- `.reshape` is a method that casts arrays to a given shape
- `.T` is an attribute (!) that holds the transformed version of a 2D array
- `np.newaxis`creates a new axis and thus change shape
- reshaped (but not regenerated!) arrays may share memory to hold the data
  - can have weird effects not unlike (mutable) lists

#%% md
### Reshaping

#%%
a = np.arange(16)
#%%
a, a.shape
#%%
a = a.reshape((2, 8))
a, a.shape
#%%
a = a.reshape((2, 2, 2, 2))
a, a.shape
#%%
a = a.ravel()
a
#%%
b = a.reshape((4, 4))
b
#%%
b[3, 2] = 42
b, a
#%%
np.shares_memory(a, b)
#%%

#%% md
### Transpose

#%%
c = b.T
b, c
#%%
np.shares_memory(b, c)
#%% md
### newaxis

#%%
a = np.arange(5)
a
#%%
b = a[:, np.newaxis]
b, b.shape
#%%
b[2, 0] = 42
b, a
#%%
b = a[:, np.newaxis, np.newaxis]
b, b.shape
#%%
b = a[np.newaxis, :, np.newaxis]
b, b.shape
#%%

#%%

#%% md
## Combination of numpy arrays

#%% md
- arrays can be combined together

#%% md
### Concatenate

#%% md
- _concatenate_ to append to existing dimensions

#%%
b0 = np.zeros(3)
b1 = np.ones(3)
b0, b1
#%%
np.concatenate((b0, b1))
#%%
b0 = np.zeros((3, 3))
b1 = np.ones((3, 3))
b0, b1
#%%
np.concatenate((b0, b1), axis=0)
#%%
c = np.concatenate((b0, b1), axis=1)
#%%
b0[0,0] = 2
c
#%% md
### Stack

#%% md
- _stack_ to generate a new dimension

#%%
b0 = np.zeros(3)
b1 = np.ones(3)
b0, b1
#%%
np.stack((b0, b1), axis=0)
#%%
np.stack((b0, b1), axis=1)
#%%
np.stack((b0, b1, b0), axis=1)
#%%

#%%

#%% md
### Split

#%% md
- _split_ to separate into subarrays

#%%
a = np.arange(16).reshape((2, 8))
b, c, d = np.split(a, (1, 3), axis = 1)#
b, c, d
#%%

#%%

#%% md
## Tasks

#%% md
##### **1.** Create an ndarray named tens of integers from 0 to 99, grouped by their "tens" digit into a 10×10 -matrix.

#%%

#%% md
##### **2.** Create an ndarray named cube labelling the vertices of a unit cube with integers from 0 to 7, by reshapeing an arange. Transpose that array. How does it change?

#%%

#%%

#%% md
##### **3.** How many different arrays can you generate by concatenating or stacking two np.eye(2) unit matrices?

#%%

#%%

#%%

#%%

#%%

#%%

#%%
