#%% md
# Pandas `Series` Objects

Pandas [`Series`](https://pandas.pydata.org/docs/reference/series.html) essentially [`np.ndarray`s](https://numpy.org/doc/stable/reference/generated/numpy.array.html) with generalized indexing capabilities. When handling `Series` you will recognize several _syntax features_ already familiar from built-in Python containers, most notably [`list`](https://docs.python.org/3/library/stdtypes.html#list) and [`dict`](https://docs.python.org/3/library/stdtypes.html#mapping-types-dict).

Let's start by noting that a [`Series` is a Python `class`](https://pandas.pydata.org/docs/reference/api/pandas.Series.html#pandas.Series). A (`Series`) class is a blueprint from which objects (of type `Series`) can be created. These objects have an _inner state_ as given by values assigned to its attributes (also called member variables in other programming languages). An object's inner state can be queried or modified by accessing its attributes or by calling methods (also called member functions) to operate on the data.

The most important attributes for our purposes are:

- [`index`](https://pandas.pydata.org/docs/reference/api/pandas.Series.index.html#pandas.Series.index): The index is used to access that actual data stored in the `Series`. The index can be such that it allows access by a label (like in a `dict`) and not just be index (like in `list`).
- [`values`](https://pandas.pydata.org/docs/reference/api/pandas.Series.values.html#pandas.Series.values): A sequence container holding the actual data of a particular `dtype` (see below). The elements can be retrieved from the container using suitable method calls that refer to the `index`.
- [`size`](https://pandas.pydata.org/docs/reference/api/pandas.Series.size.html#pandas.Series.size) (or [`shape`](https://pandas.pydata.org/docs/reference/api/pandas.Series.size.html#pandas.Series.shape)): The number of elements contained in the `Series` object. The `shape` attribute returns the `size` as a one-dimensional `tuple`.
- [`dtype`](https://pandas.pydata.org/docs/reference/api/pandas.Series.size.html#pandas.Series.dtype): The data type of the elements of the `Series`. The data type is usually one of [NumPy's data types](https://numpy.org/doc/stable/reference/arrays.dtypes.html) --- in particular if we are dealing with numerical data types like floating point or integer values.

![Pandas Series Object](img/PandasSeries-1.png)

Before we dive into the details of `Series` we have to make a few `import`s.

#%%
%matplotlib inline

import pandas as pd
import numpy as np
from matplotlib import pyplot as plt

f"Pandas version: {pd.__version__ = }, Numpy version: {np.__version__ = }"
#%% md
Note how we `import`ed the `pandas` and the `numpy` package in the execution cell: It is common to alias `pandas` with `pd` and `numpy` with `np`. By referring to these aliases we can now access functions or classes defined in the respective package. As an example we access the `Series` class from the `pandas` package:

#%%
pd.Series
#%% md
## Constructing `Series` from Python objects

A straight forward way to create `Series` object is to create them from Python sequence containers like `tuple` and `list`.

In this first example we use a `list` literal containing Python `int`s to initialize the elements of the `Series`.

#%%
integers = pd.Series([10, 30, 195, 2021])
type(integers)
#%%
integers
#%% md
Different from numpy `ndarray`, the output from the previous execution cell contains two columns where the right is the content of the `.index` attribute and the right is that of the `.values` attribute.

#%%
integers.values, integers.index
#%% md
We note that the order to the elements in the `.values` attribute is _the same_ as in the `list` used to initialize the `Series` object. `.values` returns a `np.ndarray`. The `.index` is integer-based in increases by one from one element to the next. This is the default used when we do not specify the index explicitly upon creation of a `Series` object.

To access the data we may also use the [`.array`](https://pandas.pydata.org/docs/reference/api/pandas.Series.array.html) attribute or the [`.to_numpy()`](https://pandas.pydata.org/docs/reference/api/pandas.Series.to_numpy.html#pandas.Series.to_numpy) method. The [Pandas documentation](https://pandas.pydata.org/docs/reference/api/pandas.Series.values.html#pandas.Series.values) actually recommends using one of the two possibilities to access the data stored in the `Series` object.

#%%
integers.array
#%%
integers.to_numpy()
#%% md
> Oftentimes it is not required to access the data with either `.values`, `.array`, or `.to_numpy()`; the `Series` class offers a large variety of methods to access and modify the data that are better suited in most cases.

#%% md
In a similar manner as above the we may use ` tuple` of Python `float`s to create a `Series` object.

#%%
floats = pd.Series((0.1, 0.2, 0.3))
floats
#%% md
> It is not possible to use Python containers that do not have "sequence semantics", i.e., that are not ordered. An example for such a container is a [`set`](https://docs.python.org/3/library/stdtypes.html#set).

#%%
unordered_series = pd.Series({1, 2, 3})
#%% md
### Quiz

#%% md

<span style="display:none" id="1_Series:1">W10=</span>
#%%
import jupyterquiz
jupyterquiz.display_quiz("#1_Series:1")
#%% md

<span style="display:none" id="1_Series:2">W10=</span>
#%%
pd.Series(range(5))
pd.Series({'a': 1, 'b': 2, 'c': 3})
pd.Series((1, 2, 3), index=list('abc'))
pd.Series(['a', 'b', 'c'])
int('1')
#%% md

<span style="display:none" id="1_Series:3">W10=</span>
#%%

jupyterquiz.display_quiz("#1_Series:3")
#%% md
Use the cell below as scratch space to test if the given possiblities are valid or not.

#%%
# Your code goes here!
#%% md
## Constructing `Series` from `numpy.ndarray`s

#%% md
We can also construct `Series` from `np.ndarray`s:

#%%
pd.Series(np.arange(5, dtype=np.float32))
#%% md
We note that reported `dtype` is `float32`, the same as specified for the NumPy Array.

The phenomenon of type conversion when initializing a `Series` from a Python container (which usually can hold elements with _distinct_ type) also pertains to `Series`.

#%%
pd.Series([
    1.0,  # float
    1,  # int
    complex(1, 2),  # complex
])
#%% md
### Quiz

#%% md

<span style="display:none" id="1_Series:4">W10=</span>
#%%

jupyterquiz.display_quiz("#1_Series:4")
#%% md
You are given the `ndarray`

```python
>>> a = np.array([1, 2, 3, 4, 5])
```

#%% md

<span style="display:none" id="1_Series:5">W10=</span>
#%%
a = np.array([1, 2, 3, 4, 5])
#%%
a[2], a[-3], a[1]
#%% md
Have a look at the following assignments:

```python
>>> a = np.array([0, 1, 2, 3, 4])
>>> a[:2] = [10, 20][::-1]
>>> a[-1] = -100
```

#%% md

<span style="display:none" id="1_Series:6">W10=</span>
#%%
[0, 1, 2, 3, 4, 5]
[10, 20, 2, 3, -100]
[-100, 2, 3, 20, 10]
[20, 10, 2, 3, -100]
[ ]

a = np.array([0, 1, 2, 3, 4])
a[:2] = [10, 20][::-1]
a[-1] = -100

a
#%% md
### Exercises

#%% md
#### Array creation

Write a function `make_array` that can create an array of size N with values `[0, ..., N - 1]` with a specified dtype. That is, if the N = 3 and the `dtype` is `np.int32`, the array values should be `[0, 1, 2]`. If N = 5 and the `dtype` is `np.float32` the array values are `[0.0, 1.0, 2.0, 3.0, 4.0]`. Other size-`dtype` combinations must be possible as well. The `dtype` should be defaulted to `np.int32`.

#%%
def make_array(size: int, dtype=np.int32):
    return np.arange(size, dtype=dtype)

make_array(5)
#%% md
#### Factory functions

Use two different numpy factory functions to create two `ndarray` of size 10 with values 100 (an integer!).

#%%
np.full(10, 100)
#%%
np.linspace(100, 100, 10).astype(int)
np.ones(10, dtype=int) * 100
#%% md
#### Vector norm

`ndarray`s can interpreted as vectors (e.g. a three-dimensional vector describing some directions in the 3D space we live in). Let $\boldsymbol{v} \in \mathbb{R}^3$ be such a vector. The _length_ of such a vector is defined by the equation

$$
l = \sqrt{v_1^2 + v_2^2 + v_3^3} = \sqrt{\sum_{i = 1}^3 v_i^2},
$$

where $v_i$ with $i = 1, 2, 3$ are the components of the vector. You are given the `ndarray` below and consider it to be a three-dimensional vector. Compute its length in 3 different ways.

#%%
v = np.arange(1, 4)
v
#%% md
1. Accessing the vector elements by index.

#%%
(v[0]**2 + v[1] ** 2 + v[2] ** 2) ** (1/2)
#%% md
2. Use [mathematical functions](https://numpy.org/doc/stable/reference/routines.math.html#sums-products-differences).

#%%
np.sqrt(np.sum(np.square(v)))
#%% md
3. A _single_ suitable function from NumPy's [`linalg`](https://numpy.org/doc/2.2/reference/routines.linalg.html) module.

#%%
np.linalg.norm(v)
#%% md
#### Min-max scaling

Given the array `a` below, scale all its values to lie in the range $[0, 1]$. Store the rescaled `ndarray` in a variable with named `a_scaled`.

#%%
a = np.arange(-10, 110, 10)
a
#%%
a_scaled = (a - a.min()) / (a.max() - a.min())
a_scaled
#%% md
#### Cross product

The so-called corss product between two vectors $\boldsymbol{v}, \boldsymbol{w} \in \mathbb{R}^3$ is defined by

$$
\boldsymbol{v} \times \boldsymbol{w} =
\begin{bmatrix}
v_2 w_3 - v_3 w_2\\
v_3 w_1 - v_1 w_3\\
v_1 w_2 - v_2 w_1
\end{bmatrix},
$$

where $v_i, w_i$ with $i = 1, 2, 3$ are the components of each vector. Compute the cross product of `v` and `w` defined below by accessing the right elements _by index_ and store result in a `ndarray`. You can test your result with the [`np.cross`](https://numpy.org/doc/stable/reference/generated/numpy.cross.html#numpy.cross) function.

#%%
v, w = np.array(range(1, 4)), np.array(range(3, 0, -1))
v, w
#%%
np.array(
    [
        v[1] * w[2] - v[2] * w[1],
        v[2] * w[0] - v[0] * w[2],
        v[0] * w[1] - v[1] * w[0],
    ]
), np.cross(v, w)
#%% md
## Indexing

We are aware positional indexing of Python sequence containers and `np.ndarray`s:

- Using a positive integer to refer to positions from the front or a negative integer to refer to positions from the back.
- Slicing `start:stop:step` to access a subset of elements in a systematic way.

The preferred way to access the elements of a `Series` object is using the `.loc` and `.iloc` methods which we will learn about in the next section.

#%% md
### Indexing with `.loc`, `.iloc` methods

- [`.loc[<index value>]`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.loc.html)

  - access by (index) label
  - slices include both end points

- [`.iloc[<index value>]`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.iloc.html)
  - numeric indexing with integers, from 0
  - slices exclude the end point (as with e.g. ranges)

Before we actually dive into how to use these methods, let's introduce two ways of creating a `Series` with specification of the index.

#%% md
Use a Python `dict`. The keys will be entries in `.index`, the values will be the entries in `.values`.

#%%
series_from_dict = pd.Series({'a': 10, 'b': 20, 'c': 30, 'd': 40})
series_from_dict
#%% md
Specifiy the `index` argument of the `Series` initializer (and `data` for the values to be hosted by the `Series` object).

#%%
s = pd.Series(data=range(10, 50, 10), index=['a', 'b', 'c', 'd'])
s
#%% md
To access the position in `.values` where the `.index` holds and `"a"` we need the `.loc[]` method, i.e., getting the corresponding entry from `.values` is proxied via the index. Ignoring the content of `.index` and just going by the position in the `.values` attribute means using the `.iloc[]` method.

#%%
s.loc['c'], s.iloc[2]
#%% md
Slicing is possible with both methods, however, the semantics are different. When using slicing with `.loc[]` the _end_ of the slice is included as well.

> In fact, ignoring the end would be strange if a non-sorted, non-integer index is used. Imagine the case where you have `.index` entries that are not ordered (maybe it is not even possible to establish an ordering relation). Then ignoring the end would be semantically questionable. Ignoring the end does only make sense if positional indexing is used via the `.loc[]` method.

#%%
s.loc['b':'c'], s.iloc[1:-1]
#%% md
> As you may have noticed using a step like in `start:stop:step` does not really make sense when using slicing the the `.loc[]` method.

> Note how both the `.loc[]` as well as the `.iloc[]` methods _may_ return `Series` if the slices contain more than a single element.

One final example to highlight the differences between `.loc[]` and `.iloc[]`.

#%%
yearly_numbers = pd.Series(data=[137, 214, 195, 271], index=[2014, 2016, 2018, 2020])
yearly_numbers
#%%
yearly_numbers.loc[2020], yearly_numbers.iloc[-1]
#%%
yearly_numbers.iloc[0:2]
#%%
yearly_numbers.loc[2014:2018]
#%%

#%% md
### Setting values

Like some of Python's built-in containers `Series` are _mutable_. That is, once a `Series` object has been created, its attributes (e.g., `.index`, and `.values`) may be modified. The preferred way to modify a `Series` element-wise --- the entries of `.values`, to be more precise --- is to use `.loc` (and `.iloc`) with suitable indexing.

#%%
yearly_numbers = pd.Series(data=[137, 214, 195, 271], index=[2014, 2016, 2018, 2020])
yearly_numbers
#%% md
In the following line we change the data by addressing a particular index label with `.loc[]`:

#%%
yearly_numbers.loc[2016] = 445
yearly_numbers
#%% md
We note that the reported `dtype` is `int64`. Now let's see what happens if we assign a floating point value (a Python `float`) to a location in the `Series` object. Again, we use the `.loc[]` method for this.

#%%
yearly_numbers.loc[2016] = 300.78
yearly_numbers
#%% md
Trying to fully revert this modification will not work because an integer value can be represented as floating point value. As a result, the value can be changed but the `dtype` of the `Series` object will remain `float64`.

#%%
yearly_numbers.loc[2016] = 445
yearly_numbers
#%% md
Finally, we also note that we can assign to a slice of data as well.

#%%
yearly_numbers.loc[2014:2018] = range(1000, 4000, 1000)
yearly_numbers
#%% md
> **_Warning_**
>
> While it is technically possible to mix numerical types and e.g. `string`s,
>
> ```python
> >>> yearly_numbers.loc[2014:2018] = ["this", "also", "works"]
> >>> yearly_numbers
> 2014     this
> 2016     also
> 2018    works
> 2020    271.0
> dtype: object
> ```
>
> it is _not_ recommended. The elements of the `Series` are now of type `object` (although some of them still are numbers) which will severly limit computational performance. Apart from that the semantics of operations between elements of the `Series` (e.g., adding them up) is unclear because who knows what adding a number and string is supposed to mean?

#%% md
### Non-unique indices

So far we have only dealt with cases where the content of `.index` was _unique_, i.e., none of the entries occurred more than once. With Pandas `Series`, this need not be the case (contrary to e.g. Python `dicts`). Queries for for entries with a particular index (label) have the same syntax as in case of unique `.index` entires but the return type will be _different_.

Let's have an example:

#%%
logon_times = pd.Series([10, 23, 51, 2], index=['root', 'giar', 'czerner', 'root'])
logon_times
#%% md
The index contains the `root` entry twice. Now, we query all entries with this index:

#%%
logon_times.loc['root']
#%% md
> Queries with a non-unique index return another `Series` while queries for a unique index yield an element of the particular `dtype`. The resulting `Series` has `.size` equal to the number of occurrences of the corresponding index value and the same `dtype` as the original `Series` object.

![Result of query for unique / non-unique label in index.](img/SeriesQueryLoc-1.png)

#%%
type(logon_times.loc['giar'])
#%%
logon_times['root':'czerner']
#%% md
### Fancy indexing and boolean masks

#%% md
### `Series`

We can directly use the principles that we learned for `ndarrays` for `Series`. All we have to is to call the `.loc[]` / `.iloc[]` method with either a `list` containing valid labels / positions on a `Series` object.

#%%
s = pd.Series(data=range(0, 100, 10), index=[f'a{idx}' for idx in range(10)])
s
#%% md
We set up two `list`s: One containing labels from the index of the `Series`, and another one containing positions.

#%%
labels_to_extract, positions_to_extract = (
    ["a1", "a4", "a8", "a7"],
    list(range(0, 10, 2)),
)
labels_to_extract, positions_to_extract
#%% md
We apply the `list` of index labels with the `.loc[]` method.

#%%
s.loc[labels_to_extract]
#%% md
The `list` of positions is passed to the `.iloc[]` method.

#%%
s.iloc[positions_to_extract]
#%% md
> Both operations return a new `Series` object.

#%% md
What about boolean masks? Since `Series` are somewhat of a wrapper around NumPy arrays, of course they also support extracting elements with boolean masks. To have a nice transition to the next section we will derive a boolean mask as follows:

#%%
mask =  s % 20 == 0
mask
#%% md
We have used a `Series` object in a boolean expression like we usually do it with variables like `float` or `int`. Internally, the boolean expression will be applied to every element and the results will be returned as a new `Series` object. Note, however, that the `dtype` is now reported as `bool` (it is `int64` in the original `Series`). This is because the result of a boolean expression is of type `bool`.

Now we can use this boolean mask (or `Series` of `bool`s, if you prefer this). It might appear somehow strange that `.loc[]` also accepts _another_ `Series` but, well, it works :-).

#%%
s.loc[mask]
#%% md
### Quiz

#%% md

<span style="display:none" id="1_Series:7">W10=</span>
#%%

jupyterquiz.display_quiz("#1_Series:7")
#%% md

<span style="display:none" id="1_Series:8">W10=</span>
#%%

jupyterquiz.display_quiz("#1_Series:8")
#%% md
Consider the following `Series` and the subsequent assignment:

```python
>>> s = pd.Series(data=[1.0, 2, 3.0, 4, 5.0], index=list("abcde"))
>>> s.loc["a"] = 100
```

#%% md

<span style="display:none" id="1_Series:9">W10=</span>
#%%
s = pd.Series(data=[1.0, 2, 3.0, 4, 5.0], index=list("abcde"))
s.loc["a"] = 100
s
#%% md
### Exercises

#%% md
#### Modification

You are given a `Series` with labels `["a", "b", "c", "d", "e", "f", "g", "h"]` in the index.

#%%
index = ["a", "b", "c", "d", "e", "f", "g", "h"]

s = pd.Series(data=range(1, len(index) + 1), index=index)
s
#%% md
Modify the data elements with index labels `"c"`, `"d"`, `"e"`, and "`f`" to be 10x as larger as in the initial `Series`.

#%%
s.loc['c':'f'] *= 10
s
#%% md
Set all remaining data elements to 0.

#%%
s.loc[['a', 'b', 'g', 'h']] = 0
s
#%% md
#### Boolean mask

Consider the following `Series`.

#%%
index = ["aab", "bab", "caa", "daad", "ebc", "fja", "grq", "haa"]

s = pd.Series(data=range(1, len(index) + 1), index=index)
s
#%% md
Return a `Series` that only contains index labels with two `"a"` characters.

#%%
mask = [label.count('a') == 2 for label in index]
s.loc[mask]
#%% md
Next consider this `Series`:

#%%
index = ["aab", "bab", "caa", "daab", "ebc", "fja", "grb", "haab"]

s = pd.Series(data=range(1, len(index) + 1), index=index)
s
#%% md
Return a `Series` that contains entries with index labels ending with `"b"` and that have even data elements.

#%%
mask_index_label_ends_with_b = np.array([label.endswith('b') for label in s.index])
mask_index_label_ends_with_b = s.index.str.endswith('b')
mask_is_even = s % 2 == 0
s.loc[mask_index_label_ends_with_b & mask_is_even]
#%% md
## Operations between `Series`

We will now generalize what we have just seen in the previous section to other kinds of binary operations. A _binary operator_ combines two so-called _operands_ --- a left-hand-side (lhs), and a right-hand-side (rhs) --- to compute the result of the particular operation.

```
lhs <operator> rhs
```

`Series` support the common arithmetic operations like addition, multiplication, and division, ...

- Operations are performed element-wise if the index is the default counter-based index (somewhat like adding two vectors in linear algebra).
- With other `Series`: The operations are performed _by index_. That is, operations are performed between elements that share _the same_ index label (the content of `.index`) in both `Series`.
- With scalar values: The scalar value is broadcasted to every position in the `Series` and the operation is applied there.

Needless to say, the result of such an operation is another `Series` object.

> The `dtype` of the `Series` resulting from the operation may depend
>
> - on the type operation (e.g. `bool`ean expressions, division), or
> - the `dtype` of the operands (e.g., combining a `Series` with `dtype == np.float32` and `dtype == np.int64` or `Series` with `dtype == np.int64` with a scalar of type `float`)

#%% md
### Same size and index

To start with, we first consider cases where the `Series` have _the same_ entries in `.index`.

#%%
yearly_revenue = pd.Series([4, 20, 69, 420])
yearly_expenses = pd.Series([1, 33, 7, 57])

yearly_revenue - yearly_expenses
#%%
yearly_revenue + yearly_expenses
#%%
yearly_revenue % yearly_expenses
#%% md
Let's quickly consider some examples that return a new `Series` object with a different `dtype`:

#%%
yearly_revenue > yearly_expenses
#%%
yearly_revenue * 0.55
#%%
(
    yearly_revenue // 2,
    yearly_revenue / 2,
)
#%% md
The following sketch depicts the principle of broadcasting a scalar over all elements of `Series`.

![Broadcasting a scalar over the elements of a `Series`](img/SeriesOperationsWithScalarBroadcast-1.png)

#%% md
### Match by index

Now we have a look at the more general cases where ...

- ... the index labels are ordered differently in the two `Series` combined in the operation.
- ... the index labels (partially) differ. Several cases are conceivable:
  - The `Series` have the same `.size` but the entries in `.index` differ (at least partially).
  - The `Series` have different `.size`.

We first consider the case of different orderings in `.index`. Since the operands are first matched by index the order of labels in `.index` does not matter.

#%%
yearly_revenue = pd.Series([4, 20, 69, 420], index=[2017, 2018, 2019, 2020])
yearly_expenses = pd.Series([1, 33, 7, 57], index=[2020, 2018, 2017, 2019])

yearly_revenue - yearly_expenses
#%% md
Now the case of indices with different lengths. Note how index labels are matched between both `Series` and the operation is carried out with the corresponding entries of `.values`.

The following sketch depicts the principle of matching by index in arithmetic operations between `Series`.

![Match by index in arthmetic operation between `Series` objects.](img/SeriesOperationMatchIndex-1.png)

#%%
yearly_expenses = pd.Series([1, 33, 7, 57, 12000], index=[2020, 2018, 2017, 2019, 2020])
yearly_revenue - yearly_expenses
#%% md
> In arithmetic operations `lhs <operation> rhs` between `Series` the `lhs` index is matched with that of `rhs` (if possible) and `<operation>` is carried out between elements of matching index. In cases where an index has elements with multiplicity > 1 the number of occurrences of that particular label in the index of the resulting `Series` is equal to the product of the multiplicities in `lhs` and `rhs`.

Let's have an example. `s1` has a single index label `"a"` with multiplicity 2, `s2` has index labels `"a"` and `"b"` with multiplicities 3 for `"a"`, and 1 for `"b"`. When adding both `Series` the resulting object has size 2 x 3 + 1 = 7 because each `"a"` entry from `s1` must be matched and combined with each `"a"` entry from `s2`. The +1 comes from the single `"b"` entry in `s2`.

#%%
s1, s2 = (
    pd.Series(data=[10, 20], index=["a", "a"]),
    pd.Series(data=[100, 200, 300, 1000], index=["a"] * 3 + ["b"]),
)
s1, s2
#%%
s1 + s2
#%% md
When looking at the result we note two things:

- The reported `dtype` is `float64` while the `dtype`s of both operands were `int64`.
- The value corresponding to the `"b"` entry is `NaN`.

`NaN` is short for "Not-a-Number" and results from a missing `"b"` entry in `s1`. Since `"b"` from `s2` _cannot_ does not have a match in the other `Series` there is not reasonable result to be computed, hence the result "is not a valid number". We will deal with `NaN` in more depth in the next section.

This behaviour is different to `np.ndarray`s: When adding two NumPy arrays the only thing that can be used to match the elements of both operands is the position inside the arrays. This is an important distinction between `np.ndarray`s and `Series`.

#%% md
> **_warning_** Arithmetic operations with Pandas `Series` generally do _not_ have array semantics. This means that elements from both `Series` involved in the operations are _not_ matched by their position index but rather by their index label (which may be a positional index, but not in general).

#%% md
### Exercises

#%% md
#### Min-max scaling

You are given the `Series` below as well as its smallest and largest element. Use these to map the elements to the range $[-0.5, 0.5]$.

#%%
s = pd.Series([200, 400, 600, 500, 1000, 900, 700, 300])
min_element, max_element = 200, 1000
#%%
(s - min_element) / (max_element - min_element) - 0.5
#%% md
#### Arithmetic

Consider the `Series` below that have different lengths and index entries.

#%%
s1 = pd.Series(range(4), index=list("defg"))
s2 = pd.Series(range(10), index=list("abcdefghij"))
s1, s2
#%% md
Compute the sum of both `Series`. What do you observe and why is it observed?

#%%
s1 + s2
#%% md
Return a `Series` that only contains the result of the addition but at those index entries common to both `s1` and `s2`. Find two possibilites to return such a `Series`.

#%%
(s1 + s2).dropna()
#%%
s1 + s2.loc[s1.index]
#%% md
Replace the `NaN` values in result of adding `s1` and `s2` with a value of your choice. Refer to the [documentation](https://pandas.pydata.org/pandas-docs/stable/reference/series.html) of the `Series` API to find a suitable method for this.

#%%
(s1 + s2).fillna(100)
#%% md
Use the [`.add()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.add.html) method and fill in a default value for entries missing in `s1`. What is the difference of the result compared to replacing the `NaN` values?

#%%
s1.add(s2, fill_value=100)
#%% md
(series-what-is-nan)=

## What is `NaN`?

While discussing the properties of arithmetic operations between `Series` we came across `NaN` values. In this section we will dwell on this topic a bit.

### Where do they come from ?

Let's start with the question how `NaN` values come about? `NaN`s are special value of _floating point_ data types that usually result from invalid operations on data. We refer you to [this Wikipedia article](https://en.wikipedia.org/wiki/NaN) for an exhaustive list of operations that can yield `NaN`.

For our purposes consider the (natural) [logarithm](https://en.wikipedia.org/wiki/Logarithm). Let's make a quick visualization (please ignore the details of creating the plot for time being):

#%%
x = np.linspace(0, 5, 1001)
y = np.log(x)
plt.grid()
plt.title("Natural logarithm")
plt.xlabel(r"$x$")
plt.ylabel(r"$\ln{x}$")
plt.plot(x, y)
#%% md
The logarithm goes asymptitically to $-\infty$ as $x \to 0$; it is not defined for _negative_ arguments, $x < 0$. Now, what happens if we break this rule and compute the log of, say, -1? We use the [`np.log`](https://numpy.org/doc/stable/reference/generated/numpy.log.html) function provided by NumPy.

#%%
np.log(-1), np.isnan(np.log(-1))
#%% md
The result is `NaN` as this is not a well-defined operation for this function. `NaN` is a special encoding for numerical calculations that do not have well-defined results.

> `NaN` is only available for floating points types but not for integer types. As a result, whenever Pandas encouters a `NaN` value in an operation, the the `dtype` will be changed to e.g. `float64` and all elements (also those that are not `NaN`) will be type-cast to a floating points type.

It is still possible to execute computations on `np.ndarrays` that have `NaN` entries. The result will mostly be, however, be (more) `NaN`(s) because e.g. `NaN + 3` still is `NaN`. So, whenever you encounter a `NaN` result in a computation performed with NumPy arrays (we _exclude_ Pandas objects here intentionally), it is very likely that something went wrong and you have to do some debugging.

#%%
np.sum(np.log(np.arange(-1, 5)))
#%% md
### `NaN`s in `Series`

When performing operations on `Series` Pandas chooses to deal with `NaN` values (a bit?) differently by default. We illustrate this with the following examples. We first construct a `Series` with `NaN` values and then sum all values with the `np.sum` functions that we have used earlier on a `np.ndarray` (yes, it is possible to feed `Series` objects into NumPy functions):

#%%
s = pd.Series([np.nan, *range(5), np.nan])
s
#%%
np.sum(s), np.sum(s.to_numpy())
#%% md
For comparison we also include the result of applying `np.sum` to the underlying NumPy data buffer. The results _differ_! Close inspection of the result of `np.sum(s)` reveals that Pandas chooses to _skip_ all `NaN` values and only summing those that are not `NaN`s.

> warning Pandas `Series` (and also `DataFrames`) tend to ignore `NaN` values if not told otherwise. See e.g. the documentation of the [`Series.mean`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.mean.html) method.

#%%
not_nan_mask = ~np.isnan(s.to_numpy())
s.to_numpy()[not_nan_mask].sum()
#%% md
Pandas has special methods to detect `NaN` values. Calling the [`.isna()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.isna.html) method on a `Series` returns another `Series` with `dtype == bool` of the same size where an entry equals `True` if the entry at the same position in the original `Series` was `NaN`. Otherwise an entry is `False`.

#%%
s.isna()
#%% md
The [`.notna()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.notna.html#pandas.Series.notna) method is the boolean inverse of the `.isna()` method: Its result is the element-wise negation of the output of `.isna()`.

#%%
s.notna()
#%% md
Finally, we note that you can also choose to remove `NaN` values from a `Series`. This can be accomplished with the [`.dropna()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.dropna.html#pandas.Series.dropna) method. The result is a new `Serres` object without the `NaN` entries:

#%%
s.dropna()
#%% md
### Exercise

#%%
rng = np.random.default_rng(seed=42)
#%% md
You are given a `Series` containing some `NaN` values.

#%%
data = list(range(100)) + [np.nan] * rng.choice(range(10, 50)) + list(range(50))
np.random.shuffle(data)
s = pd.Series(data=data)
s
#%% md
How many `NaN` and non-`NaN` values are contained in the `Series`?

#%%
s.isna().sum(), s.notna().sum()
#%% md
Determine the mean and the max value, as well as the standard deviation by passing the `Series` to suitable [NumPy functions](https://numpy.org/doc/stable/index.html) (use the search!)

#%%
np.mean(s), np.max(s), np.std(s)
#%% md
Determine the mean and the max value, as well as the standard deviation by using the underlying NumPy array. Of course, the results must be the same as when using the `Series`.

In fact, there are multiple ways to do this. Search the [NumPy documentation](https://numpy.org/doc/stable/index.html) for "nan" to find suitable functions.

#%%
buffer = s.to_numpy()
not_nan_mask = ~np.isnan(buffer)
np.mean(buffer[not_nan_mask]), np.max(buffer[not_nan_mask]), np.std(buffer[not_nan_mask])
#%%
np.nanmean(buffer), np.nanmax(buffer), np.nanstd(buffer)
#%%

#%% md
Come up with two different methods to return a `Series` with _all_ `NaN` values removed and `dtype == int`.

#%%
s.dropna().astype(int)
#%%
s.loc[s.notna()].astype(int)