#%% md
# Plotting with `Series`

At this point we are quite proficient with handling Pandas `Series` objects. We know how to access and modify values by calling suitable methods on a `Series` object. Indeed, the common Pandas workflow --- be it with `Series` or `DataFrame`s --- is to "massage" the data with some method calls to bring it into the desired form. This can be selecting a subset of values, transforming or aggregating values. Once the data has been prepared, the next step often is to visualize it. As discussed in the [introduction](why-bother-with-visualizing-data) compelling visualizations are key aspect of analyzing and understanding data. In the context of data science and machine learning visualizing data is the corner stone of [_exploratory data analysis_](https://en.wikipedia.org/wiki/Exploratory_data_analysis).

The Pandas library offers a quite convenient interface to the [`Matplotlib`](https://matplotlib.org/) library. While a bit aged, Matplotlib offers many powerful tools to easily generate two-dimensional plots. At the same time we should probably also mention [<PERSON>born](https://seaborn.pydata.org/) and [Plotly](https://plotly.com/python/). <PERSON>born is a wrapper around Mat<PERSON><PERSON>li<PERSON> with the goal to make commonly-used plots (e.g., histograms, scatter plots, etc.) easily accessible from a high-level Python API. Plotly also has a focus on interactive plots (zooming into plots, reading values from hovering the mouse over data points shown in the plot) which is not supported by Matplotlib.

:::{note} We will neither deal with Seaborn nor Plotly in this course.
:::

As usual we start with some imports.

#%%
%matplotlib inline

import pandas as pd
import numpy as np
import matplotlib as mpl
from matplotlib import pyplot as plt

mpl.style.use("seaborn-v0_8-colorblind")

f"Pandas version: {pd.__version__ = }, Numpy version: {np.__version__ = }"
#%% md
## `Series` plot interface

Pandas Series objects have an interface to Matplotlib that can be conventiently used to generate plots of datasets. The advantage of having a dedicated method for visualising (parts of) the data will become even more aparent when we deal with Pandas `DataFrame` objects.

`Series` have a [`.plot()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.plot.html) method that allows tho choose between different types of plots (default value is `'line'`).

Possilbe value for `kind`:

- `'line'` : line plot (default)
- `'bar'` / `'barh'`: vertical / horizontal bar plot
- `'hist'` : histogram
- `'box'` : boxplot
- `'kde'` : Kernel Density Estimation plot
- `'density'` : same as ‘kde’
- `'area'` : area plot
- `'pie'` : pie plot
- `'scatter'` : scatter plot
- `'hexbin'` : hexbin plot.

Additionally, it is also possible to call a plot method like so:

```python
>>> s.plot(kind="bar", ...) # using `.plot(kind='bar')
>>> s.plot.bar(...)  # using dedicated method for bar-plot
```

#%% md
In the following we will discuss some example usage of the `Series` plot interface on some toy data. We will also discuss some good practices for generating plots.

#%% md
## Creating an appropriate plot

In our first example we consider a `Series` containing repeated letters, i.e., each letter occurrs at least once in the `Series`. We would like to know the count of each letter and to visualise the result in a suitable manner. To get this information we use the `.value_counts()` method. The of calling this method is a new `Series` with each letter in the index and the count as values.

Consider the following table that contains repeating values (index does not matter in this example).

| index | values |
| ----: | -----: |
|     0 |      1 |
|     1 |      2 |
|     2 |      1 |
|     3 |      1 |
|     4 |      3 |
|     5 |      4 |
|     6 |      5 |
|     7 |     10 |
|     8 |     10 |

Calling the `.value_counts()` method on this `Series` yields the following:

| value | count |
| ----: | ----: |
|     1 |     3 |
|    10 |     2 |
|     2 |     1 |
|     3 |     1 |
|     4 |     1 |
|     5 |     1 |

By default the entries in the resulting `Series` are given in descending order based on their count.

#%%
rng = np.random.default_rng(seed=42)
#%%
import itertools
import string

s = pd.Series(
    data=itertools.chain.from_iterable(
        [letter] * rng.choice(range(1, 10))
        for idx, letter in enumerate(string.ascii_letters[:10])
    ),
)
#%% md
This is the output of `.value_counts()`. Additionally, the entries of the new `Series` are sorted by the content of the index.

#%%
s.value_counts().sort_index()
#%% md
Let's generate our first plot. The first thing that might come to our mind (some of you will object, but bear with us for the moment :-)) is a line plot.

#%%

#%% md
There are quite a few things we do _not_ like about this (believe, me I have people seen making such a plot for this kind of data!). For the moment we take note of the following:

- There is no title (maybe optional, if the axes labels are clear about what is actually shown).
- There are no axes labels.

How can we remedy these issues? Well, having a look at the [documentation](<(https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.plot.html)>) of the `.plot()` method (or, likewise the [`.line()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.plot.line.html)) reveals the `title`, `xlabel`, and `ylabel` parameters. Let's use these to add some additional information to our plot.

#%%

#%% md
Needless to say, there is still room for improvement. We further object that neighboring data points are connected and each data points is not clearly indicated by a dot. Furthermore, not every item name is shown on the x-axis.

The single items contained in the `Series` do not necessarily bear any relation. Hence, connecting them is rather confusing.[^connecting-points-when-they-should-not-be] We rather should indicate each count as a single stand-alone instance in the plot. One way would be to remove the lines alltogether and to add dots (or a similar symbol) instead.

[^connecting-points-when-they-should-not-be]: The single items could represent different categories in your data (see [here](https://pandas.pydata.org/pandas-docs/stable/user_guide/categorical.html)). Some notorious nit-pickers might even accuse of having "invented" additional data --- in particular if the actual data points are not clearly indicated by using an appropriate marker.

We use the following additional keywords to achieve this (see [here](https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.plot.html) for a detailed overview of possible options):

- `linestyle`: Set this to an empty string to remove the lines.
- `marker`: Specify the kind of symbol to use to indicate the actual data points.
- `xticks`: Specify the positions at which to draw ticks on the x-axis (`yticks` is also available for the y-axis). In this case we the `.nunique()` method to determine the number of unique elements in the original `Series`. This number is the same as the size of the `Series` resulting from the `.value_counts()` method.
- `grid`: Wether to draw a grid.

#%%

#%% md
In our final version of the plot we use bar plot. Each count is represented as a (vertical) bar. In this way each instance is represented as a single, self-contained piece of information in the plot. We use the `rot` keyword to specify the rotation angle used for the names of the items.

#%%

#%% md
(exercise-plotting-normalised-item-counts)=

### Exercise

Come up with _two_ different types of plot that show the _relative proportions_ in % of the single items in the above `Series`.

**Hint**: It is sufficient if the relative proportions can be estimated _visually_. We do not need the exact values here.

#%%

#%%
