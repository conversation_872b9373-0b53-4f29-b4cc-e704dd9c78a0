#%%
# Required packages
import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt
# # from matplotlib import pyplot as plt # also possible

f'{pd.__version__=}, {np.__version__=}, {mpl.__version__=}'
#%% md
# Matplotlib

Matplotlib is a plotting library for Python. It is the main plotting module for the scientific Python stack. The library provides an object-oriented API to plotting toolkits like Tkinter, Qt and other.

#%% md
## [`pyplot`](https://matplotlib.org/stable/api/pyplot_summary.html)

- [`pyplot` module](https://github.com/matplotlib/matplotlib/blob/master/lib/matplotlib/pyplot.py) offers a set of functions to create and work with figures
- `pyplot` is _stateful_, i.e. state (e.g. certain settings) is preserved across function calls
- first thing to do when intending to work with `matplotlib`:

```python
import matplotlib.pyplot as plt # commonly accepted alias for `pyplot`
```

#%%
# Global setting for better readabiliy of text
font = {"size": 15}
mpl.rc('font', **font)
#%% md
## `pyplot` functional API

#%% md
### The [`plot()`](https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.plot.html) function

```python
plt.plot(*args, scalex=True, scaley=True, data=None, **kwargs)
```

Typical call signatures:

```python
plot([x], y, [fmt], **kwargs) # [<param>]: optional parameters
plot([x0], y0, [fmt0], [x1], y1, [fmt1], **kwargs)
```

- `x`: _ordinate_ values ($x$ values)
- `y`: _abscissa_ values ($y$ values)
- `**kwargs`: Can be used for further customisation of the plots.

#%% md
Plotting a parabola: $y = f(x) = x^2$

#%%

#%%

#%%

#%% md
We can also draw multiple graphs:

#%%

#%% md
## Object-oriented API

#%% md
The object-oriented (OO) API is generally more versatile.

#%% md
### `Figure`s

- Contains the full 'canvas' for graphics output and is connected to a drawing backend (e.g. inline display, PyQT5, ...)
- [`matplotlib.Figure`](https://matplotlib.org/stable/api/figure_api.html#matplotlib.figure.Figure) class is the top-level container for everything related to the figure.

```python
# factory function from `pyplot` module that returns an empty instance
fig = plt.figure()
```

We can also use the `Figure` methods to manage the figure.

- Can contain one or more `Axes` objects.

#%% md
### `Axes`

- Region of the figure containing drawn data. A `Figure` can contain one or more `Axes` instances.
- [`Axes`](https://matplotlib.org/stable/api/axes_api.html#matplotlib.axes.Axes) contain _two_ [`Axis`](https://matplotlib.org/stable/api/axis_api.html#matplotlib.axis.Axis) ("$x$-axis" and "$y$-axis") objects that are used to manage the range of shown values (like a number-line).
- Obtaining a `Axes` instance:

```python
# Get instance of `Figure` and `Axes` class from `subplots()` factory function.
fig, ax = plt.subplots() # `Figure` instance with single `Axes`
```

- `matplotlib.axis.Axes` class implements methods to manage drawings. These member functions are the most common way of using the object-oriented API. For example:
  - `ax.set_title()`: The title of current `Axes`.
  - `ax.set_xlim()`, `ax.set_ylim()`: Set limits for value ranges along $x$- and $y$-axis.

#%% md
The "anatomy of a Matplotlib figure" is detailed [here](https://matplotlib.org/stable/_images/sphx_glr_anatomy_001.png).

#%%

#%% md
With the OO API we can easily create several `Axes` objects with the same `Figure`.

#%%

#%% md
## Customising plots

#%% md
Visualising data is a key aspect of scientific work. While giving a condensed view on data (in the sense that raw numbers are not shown) plots can unravel important properties of data. We therefore must make sure to choose decent graphical representations.

#%% md
### Lines and markers

During measurements samples usually cannot be continuously obtained. Data is obtained at a finite set of discrete values.

#### Example

Measuring a car's velocity at constant acceleration.

| time / s | velocity / m$\cdot$s$^{-1}$ |
| :------: | :-------------------------: |
|   0.4    |             2.5             |
|   2.2    |            22.0             |
|   4.2    |            41.5             |
|   6.1    |            61.0             |

#%%
rng = np.random.default_rng()

num_points = 11

time = np.linspace(0.25, 10, num=num_points)
velocity = 10 * time + rng.normal(loc=0, scale=2, size=(num_points,))

data = pd.Series(index=time, data=velocity)
data
#%%

#%% md
#### Comments

#%% md
- Line-only plot
  - Looks like we have more data points than actually measured (measured data cannot be easily identified)
  - Artificially connects data points
  - $\Rightarrow$ Misleading representation of data

#%% md
- Line + markers plot
  - Decently shows data obtained from measurement
  - Connection between data points still artificial but we may use it as "guide to the eye" (this should be clarified in e.g. in a figure caption)

#%% md
- Marker-only plot
  - Decently shows data obtained from measurement
  - Often accompanied with fit

#%%
styles = {"marker": "o", "markersize": 8.5, "linestyle": "", "linewidth": 2.5}
#%%

#%% md
Different types (shapes) of [markers](https://matplotlib.org/stable/api/markers_api.html) are available from Matplotlib.

#%%
fig, axes = plt.subplots(3, 3, sharex='col', sharey='row', figsize=(15, 10))
for idx, (ax, marker) in enumerate(
    zip(axes.ravel(), ("o", "v", "8", "s", "P", "D", "d", ">", "<"))
):
    if (idx + 1) % 3 == 1:
        ax.set_ylabel(r"velocity / m$\cdot$s$^{-1}$")
    if idx > 5:
        ax.set_xlabel("time / s")
    ax.plot(data.index, data.values, marker=marker, markersize=10, linestyle="")
#%% md
Different linestyles are also possible.

Allowed values for `linestyle` argument of the `plot()` function are:

```python
'-', '--', '-.', ':', 'None', ' ', '', 'solid', 'dashed', 'dashdot', 'dotted'
```

#%%
x_values = np.linspace(-2 * np.pi, 2 * np.pi, num=1001)

plot_config = (
    dict(shift=-np.pi / 3, ls="-", lw=2.5, label=r"$\theta=\pi/3$"),
    dict(shift=0, ls="--", lw=2.5, label=r"$\theta=0$"),
    dict(shift=np.pi / 2, ls=":", lw=2.5, label=r"$\theta=\pi/2$"),
    dict(shift=np.pi, ls="-.", lw=2.5, label=r"$\theta=\pi$"),
)
#%%
plt.xlabel(r"$x$")
plt.ylabel(r"$\sin(x-\theta)$")
for conf in plot_config:
    plt.plot(
        x_values,
        np.sin(x_values - conf["shift"]),
        linestyle=conf["ls"],
        linewidth=conf["lw"],
        label=conf["label"],
    )
plt.legend()
#%% md
Colors for lines in plots can also be chosen manually.

For more information on how to use colors with Matplotlib refer to the followin links:

- https://matplotlib.org/stable/api/colors_api.html#module-matplotlib.colors
- https://matplotlib.org/stable/gallery/color/color_demo.html#sphx-glr-gallery-color-color-demo-py
- https://matplotlib.org/stable/tutorials/colors/colors.html#sphx-glr-tutorials-colors-colors-py

#%% md
We can specify colors in multiple ways:

- RGB tuple: `(r_value, g_value, b_value)`, or RGBA tuple: `(r_value, g_value, b_value, alpha)`. The value inside the tuple are float values from the closed interval `[0, 1]`.
- HEX strings
- Named colors from e.g. https://matplotlib.org/stable/gallery/color/named_colors.html or https://xkcd.com/color/rgb/

#%%
# Matplotlib can deal with named colors as well as HEX strings

named_colors = ("black", "red", "blue", "cyan")

hex_colors = (
    "#000000",  # "xkcd:blacks"
    "#e50000",  # "xkcd:red"
    "#0343df",  # "xkcd:blue"
    "#00ffff",
)  # "xkcd:cyan"
#%%
fig, (ax1, ax2) = plt.subplots(nrows=1, ncols=2, figsize=(10, 4))

for ax in (ax1, ax2):
    ax.set_xlabel(r'$x$')
ax1.set_ylabel(r'$\sin(x - \theta)$')

for color, conf in zip(named_colors, plot_config):
    ax1.plot(
        x_values,
        np.sin(x_values - conf["shift"]),
        color=color,
        linestyle=conf["ls"],
        linewidth=conf["lw"],
        label=color,
    )
ax1.legend()

for color, conf in zip(hex_colors, plot_config):
    ax2.plot(
        x_values,
        np.sin(x_values - conf["shift"]),
        color=color,
        linestyle=conf["ls"],
        linewidth=conf["lw"],
        label=color,
    )
ax2.legend()
#%% md
It is often preferrable to separate data in multiple `Axes` objects. This will greatly enhance clarity.

#%%
fig, axes = plt.subplots(
    nrows=len(plot_config), ncols=1, sharex="col", sharey=True, figsize=(10, 8)
)

for idx, (ax, conf) in enumerate(zip(axes, plot_config)):
    ax.set_ylabel(r"$\sin(x-\theta)$")
    ax.grid(linestyle="--", linewidth=0.5)
    ax.plot(
        x_values,
        np.sin(x_values - conf["shift"]),
        linewidth=conf["lw"],
        label=conf["label"],
    )
    ax.legend(loc="upper right")
axes[-1].set_xlabel("$x$")
#%% md
A common use case of gathering multiple datasets in _one_ `Axes` object is if we want to trace trends.

#%%
def gaussians(x_values, sigma, offsets, weights):
    def gauss(x, sigma, offset):
        pre_factor = 1 / (sigma * np.sqrt(2 * np.pi))
        exp_factor = 1 / (2 * sigma**2)
        return pre_factor * np.exp(-((x - offset) ** 2) * exp_factor)

    return np.dot(
        weights,
        gauss(x_values, sigma, offsets[..., np.newaxis]),
    )


annealing_temperature = np.arange(100, 1001, 100)
#%% md
#### Note

The data used for the following figure has _not_ been obtained from real measurements.

#%%
x_values = np.linspace(20, 65, num=10001)

_, ax = plt.subplots(figsize=(10, 5))
ax.set_title("XRD intensity vs. annealing temperature")
ax.set_xlabel(r"angle / degrees")
ax.set_ylabel(r"intensity / arb. units")
ax.grid()  # Grids can be helpful for orientation inside the figure.
ax.minorticks_on()  # Helpful for details.

for temp, damp in zip(annealing_temperature[::-1], np.arange(0.1, 1.1, 0.1)[::-1]):
    ax.plot(
        x_values,
        damp * gaussians(x_values, 1.5, np.array((30, 50)), (1, 0.4)),
        label=r"%d${}^{\circ}$C" % (temp,),
    )

ax.legend(loc="upper right", fontsize=16)
#%% md
Another possible data encoding scheme is to use colors to encode additional degrees of freedom.

Matplotlib offers many colormaps for different purposes. Colormaps must be chosen with care; for an in-depth discussion of things to consider (e.g. colorblind-friendly colormaps) please refer to [this website](https://matplotlib.org/stable/tutorials/colors/colormaps.html).

Refer to the [colormap reference](https://matplotlib.org/stable/gallery/color/colormap_reference.html).

#%%
import matplotlib.cm as cmap
import matplotlib.colors as colors

# We will encode the annealing temperature in colors. Lines representing data related to higher temperatures will be
# shown as lighter colors while lines related to data from lower-temperatures will be shown in darker colors.

color_map = plt.get_cmap("plasma")
# Normalise colormap to current range of values.
color_norm = colors.Normalize(
    vmin=-250 + np.min(annealing_temperature), vmax=+200 + np.max(annealing_temperature)
)
temp_to_color = cmap.ScalarMappable(norm=color_norm, cmap=color_map)
#%%
x_values = np.linspace(20, 65, num=10001)

_, ax = plt.subplots(figsize=(10, 5))
ax.set_title("XRD intensity vs. annealing temperature")
ax.set_xlabel(r"angle / degrees")
ax.set_ylabel(r"intensity / arb. units")
ax.grid()  # Grids can be helpful for orientation inside the figure.
ax.minorticks_on()  # Helpful for details.

for temp, damp in zip(annealing_temperature[::-2], np.arange(0.1, 1.1, 0.1)[::-2]):
    ax.plot(
        x_values,
        damp * gaussians(x_values, 1.5, np.array((30, 50)), (1, 0.4)),
        label=r"%d${}^{\circ}$C" % (temp,),
        color=temp_to_color.to_rgba(temp),
    )

ax.legend(loc="upper right", fontsize=16)
#%% md
### Axes styles

#%% md
In case the units as well as the precise values at a particular axis are not important we leave them out.

#%%
x_values = np.linspace(20, 65, num=10001)

_, ax = plt.subplots(figsize=(10, 5))
ax.set_title("XRD intensity vs. annealing temperature")
ax.set_xlabel(r"angle / degrees")
ax.set_ylabel(r"intensity / arb. units")
ax.set_yticklabels([])
ax.grid()  # Grids can be helpful for orientation inside the figure.
ax.minorticks_on()  # Helpful for details.

for temp, damp in zip(annealing_temperature[::-2], np.arange(0.1, 1.1, 0.1)[::-2]):
    ax.plot(
        x_values,
        damp * gaussians(x_values, 1.5, np.array((30, 50)), (1, 0.4)),
        label=r"%d${}^{\circ}$C" % (temp,),
        color=temp_to_color.to_rgba(temp),
    )

ax.legend(loc="upper right", fontsize=16)
#%% md
We can set the ticks as well as the tick labels for the axes ourselves.

#%%

#%% md
The range of values along an axis can be set manually.

#%%

#%% md
#### log-linear and log-log plots

#%%
x_values = np.linspace(0.1, 10)


def fpower1(x, a, b):
    return a * x_values**b


def fpower2(x, a, l, g):
    return l * a ** (g * x_values)
#%% md
#### log-log plot

If we have a functional relationship of the form $f(x) = y = a x^m$ we can take the logarithm to base $B$ (any base) on both sides to obtain:

$$
\log_B y = \log_B\left(a x^m\right) = \log_B a + \log_B \left(x^m\right) = \log_B a + m \log_B x
$$

Setting $Y = \log_B y$, $X = \log_B x$ and $b = \log_B a$ we arrive at a linear equation

$$
Y = m X + b
$$

with slope $m$ and (log $y$)-axis intercept $b$.

#%%
fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(20, 4))

a, b = 2, 3

ax1.set_title(r"linear-linear plot")
ax1.set_xlabel(r"$x$")
ax1.set_ylabel(r"$y$")


ax2.set_title(r"$\log_{10}$-$\log_{10}$ plot of " + r"$y = a x^b$")
ax2.set_xlabel(r"$x$")
ax2.set_ylabel(r"$y$")
ax2.set_xscale("log")
ax2.set_yscale("log")


ax3.set_title(r"plot of " + r"$\log_{10}y = \log_{10}(a x^b)$")
ax3.set_xlabel(r"$\log_{10}x$")
ax3.set_ylabel(r"$\log_{10}y$")
ax3.set_xscale("linear")
ax3.set_yscale("linear")

#%% md
#### log-linear plot

If we have a functional relationship of the form $f(x) = y = \lambda a^{\gamma x}$ we can take the logarithm to base $B$ (any base) on both sides to obtain:

$$
\log_B y = \log_B\left(\lambda a^{\gamma x}\right) = \log_B \lambda + x \gamma \log_B a
$$

Setting $Y = \log_B y$, $m = \gamma \log_Ba$ and $b = \log_B\lambda$ we obtain a linear equation

$$
Y = m x + b
$$

with slope $m$ and (log $y$)-axis intercept $b$.

#%%
fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 4))

a, l, g = 10, 2, 0.5

ax1.set_title(r"linear-linear plot")
ax1.set_xlabel(r"$x$")
ax1.set_ylabel(r"$y$")


ax2.set_title(r"$\log_{10}$-linear plot of " + r"$y = \lambda a^{\gamma x}$")
ax2.set_xlabel(r"$x$")
ax2.set_ylabel(r"$y$")
ax2.set_xscale("linear")
ax2.set_yscale("log")


ax3.set_title(
    "plot of " + r"$\log_{10} y = \log_{10}\left(\lambda a^{\gamma x}\right)$"
)
ax3.set_xlabel(r"$x$")
ax3.set_ylabel(r"$\log_{10} y$")
ax3.set_xscale("linear")
ax3.set_yscale("linear")

#%% md
### 2.6 Tasks

#%% md
##### 1. Dampened harmonic oscillator

Plot the dampened harmonic oscillation $f(t) = \mathrm{e}^{-t}\cdot\sin(2\pi\cdot 3 \cdot t)$ in the time interval $[0s, 5s]$, as well as the upper and lower exponential envelope. Add a legend and axis labels. Limit the x range to $[0, 5]$. How many points on the x-axis do you need to use in order for the graphs to appear smooth?

#%%

#%% md
##### 2. Points in a circle

Gather a random array `P` of shape `(2, N)` with `N=1000` uniformly from the range `[-1, 1]`. Interpret this as N 2D points and draw a scatterplot. Draw an unfilled circle on top, with radius 1 and origin (0, 0). Now, sort P into two arrays, `P_inside` and `P_outside`, depending on the points being inside or outside of the circle, using numpy. Plot the points inside in a different color. What is `P_inside.shape[0]/(P.shape[0]/4) ?`

#%%

#%% md
## Tasks

#%% md
### Konzentration von reaktiven Gasen

Sie erhalten Daten für die Konzentration zweier gasförmiger Chemikalien (Einheit: $\text{mol m}^{-3}$) in Abhängigkeit von der Zeit (Einheit: $\text{s}$). Es handelt sich um die Gase Ozon ($\text{O}_3$) und Stickstoffmonoxid ($\text{NO}$). Diese Gase werden in der Atmosphäre abgebaut. Stellen Sie die Zeitabhängigkeit der Konzentration sinnvoll dar, sodass der funktionale Zusammenhang sichtbar wird.

#%% md
#### Ozon

#%%
c_ozone = pd.Series(data=(1, 0.786, 0.649, 0.482, 0.381), index=(0, 50, 100, 200, 300))
#%%

#%% md
#### Stickstoffmonoxid

#%%
c_no = pd.Series(data=(8.13, 5.07, 2.41, 0.78, 0.36), index=(0, 50, 100, 200, 300))
#%%

#%% md
#### 2. Reaktionskinetik

Reaktionsraten $k$ (Einheit: $\text{s}^{-1}$) bei verschiedenen Temperaturen $T$ (Einheit: $\color{red}{\text{K}}$) gehorchen erwartbar folgendem Gesetz (mit einer Naturkonstante $R$ und freien Parametern $k_0$, $E_A$):
$$k = k_0 \cdot \exp\left(\frac{-E_A}{R T}\right)$$

Sie erhalten Daten für $k$ (in $\text{s}^{-1}$) und $T$ (in $\color{red}{^\circ\text{C}}$).
Finden Sie eine geeignete Auftragung, die Ihnen die Bestimmung von $k_0$ und $E_A$ ermöglicht!

#%%
reaction_rates = pd.Series(data=(2.52, 5.25, 63, 316), index=(190, 199, 230, 251))
#%% md
##### Begin solution

Durch Umstellen der Gleichung erhält man
$$ \ln(k) = \ln(k_0) + \frac{-E_A}{RT}$$

Wenn man also $\ln(k)$ gegen $\frac{1}{T}$ aufträgt, erhält man eine Gerade mit Steigung $\frac{-E_A}{R}$ und Achsenabschnitt $\ln(k_0)$.

#%%
