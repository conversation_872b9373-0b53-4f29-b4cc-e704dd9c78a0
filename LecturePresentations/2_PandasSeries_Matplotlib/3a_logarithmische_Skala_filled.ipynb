#%%
%matplotlib inline

# Required packages
import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt

f'{pd.__version__=}, {np.__version__=}'
#%% md
# Addendum: logarithmic scale
#%%
data = pd.Series(10 ** np.arange(10))

fig, axs = plt.subplots(1, 2, figsize=(15, 5))
axs[0].plot(data.index, data.array, marker='o', linestyle='')
axs[1].semilogy(data.index, data.array, marker='o', linestyle='')
#%%
rng = np.random.default_rng(seed=42)
#%%
data = 10 ** np.arange(10)
rng.shuffle(data)
data = pd.Series(data)
data
#%%
fig, axs = plt.subplots(1, 2, figsize=(15,5))
axs[0].bar(data.index, data.array)
axs[1].set_yscale('log')
axs[1].bar(data.index, data.array)
#%%
