#%% md
# Plot Customization with Mat<PERSON><PERSON>li<PERSON>

So far we have used the plotting interface available from Pandas `Series`. While this offers lots of useful functionality, we would like to have a more fine-grained control over the appearance of our plots.

Pandas conventionally wraps Matplotlib as a plotting backend. Indeed, when calling the [`.plot()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.plot.html) method on a `Series` object a Matplotlib [`Axes`](https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html) object is returned. This has many "knobs" allowing us to fine-tune the appearance of a particular plot.

:::{note} Below we will discuss some example use cases of the Matplotlib's object-oriented API. Please do not expect a complete treatment of a functionalities in this section. Clearly, this would be out-of-scope for this course. We rather want to understand the basic principles of working with this type of API and to know the customization points. For a more detailed overview we refer you to Matplotlib's [example gallery](https://matplotlib.org/stable/gallery/index.html).
:::

As usual we start with some imports.

#%%
%matplotlib inline

import pandas as pd
import numpy as np
import matplotlib as mpl
from matplotlib import pyplot as plt

mpl.style.use("seaborn-v0_8-colorblind")

f"Pandas version: {pd.__version__ = }, Numpy version: {np.__version__ = }"
#%% md
## Customizing plots

#%% md
Let's return to our [initial example](creating-an-appropriate-plot) for generating a plot. We created a bar plot of some ficticious data containing several items (categories).

#%%
rng = np.random.default_rng(seed=42)
#%%
import itertools
import string

s = pd.Series(
    data=itertools.chain.from_iterable(
        [letter] * rng.choice(range(1, 10))
        for idx, letter in enumerate(string.ascii_letters[:10])
    ),
)
#%% md
We have noted ealier that calling the `.plot()` method returns an `Axes` object. Therefore, we assign the output to a variable names `ax.` Indeed, this is the commonly chosen name for such kind of objects when working with Matplotlib.

The thus generated plot is still requires some customization. While we could use suitable arguments for the `.plot()` method (in practice we would do so!) --- or the `.bar()` method in this example --- we will call appropriate methods available for `Axes` object to conduct the customization like setting the title, or adding axis labels.

#%%

#%% md
We quickly check for the type of `ax`.

#%%

#%% md
Now we repeat the plot and add

- a title,
- axis labels, and
- horizontal grid lines.

The `ax` object effectively provides us with a handle to the plot (the x- and the y-axis, in particular). We cite from the [Matplotlib manual](https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html)

> An Axes object encapsulates all the elements of an individual (sub-)plot in a figure.

As we see below there are some `get`ter and `set`ter methods available to for instance set labels for the axes or manipulating the labels for the ticks along the x-axis.

#%%

#%% md
While most of the method calls should be straight-forward to understand, the call to `.set_xticks()` requires some explanation: The purpose of calling this method is to rotate the labels of the ticks on the x-axis. The method expects the tick-positions in a sequence container (e.g. a `list` or a `np.ndarray`) and the labels e.g. as a `list`. During the call to `.bar()` these positions and labels have already been created; we just want to _customize_ their appearance. Therefore, we call the `get`methods `.get_xticks()` and `.get_xticklabels()` that return us the currently set positions and labels.

#%%

#%% md
These are passed are arguments to the `.set_xticks()` method; the only thing left to do is to specify the `rotation` parameter.

#%% md
## Subplots

Oftentimes we want certain pieces of information to be visualized side-by-side. To achieve this we can use subplots embedded within a Matplotlib [`Figure`](https://matplotlib.org/stable/api/figure_api.html#matplotlib.figure.Figure).

A grid of subplots can be conveniently created with the [`plt.subplots()`](https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.subplots.html) function.

```python
>>> fig, (ax1, ax2) = plt.subplots(nrows=1, ncols=2, figsize=(12, 5))
```

In this example we create a 1 x 2 layout (1 row, 2 columns); the return value is a `tuple` containing a `Figure` object and a `np.ndarray` (possibly nested) of `Axes` instances. Here the size of the NumPy array containing the `Axes` objects is 2, so it is instructive to use `tuple` unpacking to assign individual names to the single instances. The width and the height of the canvas on which the plots will be drawn is specified with the `figsize` parameters: `( width, height )`.

Below we revisit the example from an earlier [exercise](exercise-plotting-normalised-item-counts) in which you were asked to plot the relative proportions of items. The left plot (references by `ax1`) contains a bar chart with normalized item counts, while the right plot (references by `ax2`) contains a pie chart. Note how the labels and the values of the proportions have been customized in the plot.

#%%

#%% md
Another way to design the layout of a figure with multiple subplots is the [`.subplot_mosaic()`](https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.subplot_mosaic.html) function. We will not discuss every detail of the code below. What is shown is a so-called scatter plot which basically just plotting $(x, y)$ pairs of data. This plot is shown as dots below. Additionally, there are two histogram attached at the top and right of the scatter plot. The top plot show the distribution of the $x$ values, while that on the right show the distribution of the $y$ values.

#%%
fig, axes = plt.subplot_mosaic(
    [
        ["histogram-x", "."],
        ["scatterplot", "histogram-y"],
    ],
    width_ratios=[2, 1],
    height_ratios=[1, 2],
    gridspec_kw={"hspace": 0.1, "wspace": 0.1},
)

(
    x_values,
    y_values,
) = (
    rng.normal(size=250),
    rng.normal(loc=4, size=250),
)

# Scatter plot in the middle
ax = axes["scatterplot"]
ax.minorticks_on()
ax.set_xlabel("x")
ax.set_ylabel("y")
ax.scatter(x_values, y_values)

# Histogram on the right-hand side
ax = axes["histogram-y"]
ax.set_yticklabels([])
ax.set_xlabel("count")
ax.hist(y_values, orientation="horizontal")

# Histogram at the top
ax = axes["histogram-x"]
ax.set_xticklabels([])
ax.set_ylabel("count")
ax.hist(x_values, bins=10);
#%% md
### Exercises

#%% md
#### Sine functions with phase shift

Create a single plot with four sine functions, each have a different phase $\phi$ in the range $[-2\pi, 2\pi]$:

$$
f(x) = \sin(x - \phi)
$$

Use angles $\phi = 0, \pi / 3, \pi / 2, \pi$. For each sine function set the linestyle as well as the color manually. Search the [documentation](https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.plot.html) of the `.plot()` function to find the right options to conduct these customizations. Make sure that each line can be associated with a particular phase shift.

_Hints_:

- Use the [`np.linspace`](https://numpy.org/doc/stable/reference/generated/numpy.linspace.html) function to generate the range of x-values.
- Compute each sine with a [NumPy function](https://numpy.org/doc/stable/reference/routines.math.html).

#%%

#%% md
#### Sine functions with phase shift in subplots

Plot the same sine functions as in the previous exercise but now plot each of the functions in a _separate_ subplot in a 4x1 plot grid. Assure that all subplots have the same x-scale and y-scale. The values on the x-axis shall be in degree units.

#%%

#%%

#%%
