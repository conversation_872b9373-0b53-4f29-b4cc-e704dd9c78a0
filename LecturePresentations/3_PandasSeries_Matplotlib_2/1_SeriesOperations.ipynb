#%% md
# Operations on `Series`

In the introduction on Pandas `Series` we noted that they are objects and as such have inner state given by the attributes (e.g., `.index` and `.values`) and methods to query and modify this inner state. So far we have just touched on methods more or less accidentially (cf. the `NaNs` in `Series` section).

In this section we will spent some time exploring different types of methods available for Pandas `Series`. As we will learn, calling methods --- and in particular chaining several method calls (so-called method chaining) --- is a quite common pattern when working with Pandas.

An exhaustive list of available methods can be found in the [`Series` documentation](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.html)).

As usual we start with some imports.

#%%
%matplotlib inline

import pandas as pd
import numpy as np
from matplotlib import pyplot as plt

f"Pandas version: {pd.__version__ = }, Numpy version: {np.__version__ = }"
#%% md
## Statistics in `Series`

Series have a number of methods for performing basic statistics. The result of the corresponding methods call may vary: Some (may) yield other `Series` while others yield scalar values.

#%% md
### Reductions

Reductions are operations that map the content of a `Series` to a single scalar value. The principle is illustrated in the following sketch.

![`Series` reductions: Summation, mean value, and standard deviation](img/Reductions-1.png)

> **note** We note that reductions are most commonly used with numerical data. In particular mean value, the standard deviation, or the median strange quantities in the context of non-numerical data such as strings.

#%%
s = pd.Series(range(1, 10))
s
#%% md
Sum all elements in the `Series` to yield a scalar value. The result usually is of the same type as the `dtype` of the `Series`.

#%%

#%% md
The mean value is the sum over all elements devided by the size of the `Series`. Due to the division operation result has a floating point type.

$$
\mu = \frac{1}{N}\sum_{i = 0}^{N - 1}s_i,
$$

where $N$ is the size of the `Series` and $s_i$ are the elements of the `Series`, $ i = 0, \dots, N - 1$.

#%%

#%% md
The computation of the standard deviation deserves a bit of explanation. In _Pandas_ it is implemented like

$$
\sigma = \sqrt{\frac{1}{N - \Delta} \sum_{i = 0}^{N - 1}\left(s_i - \mu\right)^2},
$$

and $\Delta = 1$. The $\Delta$-value is important; this is also called "the degree of freedom". NumPy, on the contrary computes the standard deviation with $\Delta = 0$. When using the [`np.std`](https://numpy.org/doc/stable/reference/generated/numpy.std.html) function we have to specify the `ddof` parameter to obtain the same result as obtained from calling `.std()` on a `Series`.

#%%

#%% md
We note that the variance is the standard deviation squared, $\sigma^2$.

#%%

#%% md
Another important statistics to be computed is the median.

#%%

#%% md
The [`.agg()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.agg.html) method allows to compute multiple reductions at once. In contrast to the specialized methods used so far, the result _may be_ another `Series` object. The method also accepts a `list[str]`, where each `str`ing is a valid name of a reduction (e.g. `"mean"` for the mean value, or `"median"` for the median value).

> `.agg()` will return a `Series` if the argument `func` is of type `list` (more generally, an iterable). Even if the `list` has just a single argument (like in `s.agg(["mean",])`) the result will be a new `Series` of size equal to 1.

#%%

#%% md
Actually, it is also possible to pass a `list[callable]` (a `list` of `callable` functions) where each of the functions must compute some sort of reduction. In practice, this will often by NumPy ufuncs like [`np.mean`](https://numpy.org/doc/stable/reference/generated/numpy.mean.html), or [`np.std`](https://numpy.org/doc/stable/reference/generated/numpy.std.html).

#%%

#%% md
As a final example for reductions we consider passing custom functions in a `dict`. The keys specify the index label of the result in the resulting `Series`.

#%%

#%% md
Each of the functions defined inside the `dict` operates on the whole `Series` on which the `.agg()` method is called. The expression `lambda x: (x ** 2).sum()` defines an anonymous `lambda` function that takes a `Series` as argument `x`, squares it `(x ** 2)` (this is another `Series`!), and then calls `.sum()` on the new `Series` resulting from the previous operation. The result of the summation is returned and is the entry associated with the label `"sum of squares"`.

> An alternative to compute the sum of squares is to chain appropriate method calls: `lambda x: x.pow(2).sum()`. `x` is a `Series`, on which we call `pow(2)` (compute each element to the power of 2) which returns a _new_ `Series`, on which we call `.sum()` to compute the actual reduction.

#%% md
### Methods yielding other `Series` (or something similar)

Apart from reductions there also exist "statistical" methods that may return a new `Series`.

#%%

#%% md
The `.value_counts()` methods returns a `Series` with the frequency of values. The index of the resulting `Series` are the unique entries of the original `Series` object, while data it holds is the count of each of the unique values.

#%%

#%% md
The unique values can either be obtained from the index of the previous result or we use the `.unique()` method.

> note The `.unique()` method returns the its result as a `np.ndarray`.
#%%

#%% md
As the last method discussed in this section we take the [`.duplicated()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.duplicated.html) method. This method returns a new `Series` with `dtype == bool` that has `True` at the position of each duplicate value. One of the values with multiplicity > 1 in the `Series` is marked with `False`.

#%%
s = pd.Series(index=[f"a{idx}" for idx in range(len(s))], data=s.to_numpy())
s
#%%

#%% md
We can use this `Series` as a boolean mask to extract the the unique values from the `Series`. The `~` in front of `s.duplicated()` _negates_ all entries in the `Series`; this is a syntactic sugar for calling [`np.logical_not`](https://numpy.org/doc/stable/reference/generated/numpy.logical_not.html). We note that the index of the `Series` we obtain contains the labels at which the `~s.duplicated()` has `True` entries.

> **Reminder** An exhaustive list of NumPy's logical functions can be found on: https://numpy.org/doc/stable/reference/routines.logic.html.

#%%

#%%

#%% md
Before closing this section we will demonstrate that the `.value_counts()` method can also be used for non-numerical data, `str` for example.

> **note** The `dtype` of a `Series` containing `str` objects is `object`.

#%%
words = pd.Series("In Ulm und um Ulm und um Ulm herum".split())
words
#%%

#%%

#%% md
## `Series` manipulation

Much of the work with `Pandas` data structures is done with calling appropriate methods on objects. Indeed, chaining method calls is a commonly observed pattern found in Pandas workflows.

In the following we will discuss some helpful methods used to manipulate content of a `Series` object.

> **warning** all methods discussed here return _new_ objects. That is, they either return a _new_ `Series` (e.g. by transforming the content of another `Series`) or new scalar values (e.g. resulting from an aggregation).

The principle of use methods calls on objects is sketched in the following figure (we will deal with the `.transform()` and the `.apply()` method below).

![Calling methods on Pandas `Series` objects.](img/SeriesMethods-1.png)

#%% md
### `.replace()` and `.map()`

The methods [`.map()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.map.html) and [.`replace()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.replace.html) are used to replace different values according to a replacement rule.

We start with `.replace()`. You will soon realize that methods called on `Series` (and `DataFrame`s as well) accept quite a lot of different types of arguments (e.g., `str`, `list`, `dict`, and `callable`s). This is also true for `.replace()` (refer to the `to_replace` parameter) as we will now see.

#%%
strings = pd.Series("Er sah das Wasser as".split())
strings
#%% md
The following method call will replace the entry `'as'` with `'an'`: We use two (positional) arguments to replace any occurrence of the first with the second. It is important to note that an entry must _exactly_ match the string `'as'` --- no characters to the left or the right are allowed. As a result, the substrings in`'das'` or `'Wasser'` will _not_ be replaced with `'an'`.

#%%

#%% md
If we want to `'as'` to be interpreted as a pattern that shell be replaced we must use [regular expressions](https://docs.python.org/3/howto/regex.html). We will, however, not dwell too long with "regexs" as this is topic for itself. Suffice it to say, that regular expressions are often very helpful when working with strings.

#%%

#%%

#%% md
Python `dict`s are good for expressing replacement rules: The keys describe what to replace (old values) while the values describe what to fill in instead (new values).

> **note** Replacements are made for (old) values that are _explicitly_ specified while all others are _ignored_. As a result the size of the old and the new `Series` will be the same. Needless to say they, they also have the same index.

#%%
integers = pd.Series((0, 10, 20, 30))
integers
#%%

#%% md
The same result can also be achieved with passing a `Series` as argument.

#%%

#%% md
To quickly demonstrate the a call to `.replace()` actually returns a _new_ `Series` --- that does _not_ share memory with the original `Series` object it was created from --- we inspect the content of the original `Series`:

#%%
integers
#%% md
As we can see the content of the original `Series` is unchanged. In fact, calling `.replace()` on `integers_with_replaced` would yield another `Series` independent of that it was created from (and so forth).

The `.map()` method is also available for making replacements. The semantics are slightly different, however. Let's start with the example from above where we used a `dict` to specify the replacements rules:

#%%

#%% md
We note that _all_ values not captured by the replacement rules have been replaced with `NaN`. As a result the `dtype` has been changed to `float64` (remember that `NaN` is a special floating point value). If we do not want the `NaN` values we can use a [`defaultdict`](https://docs.python.org/3/library/collections.html#collections.defaultdict) with a suitable (whatever your current situation demands) default value.

#%%

#%% md
Instead of letting values not taken into account in the replacement specification being converted to `NaN`s, we specify a default replacment value for them (-5000 in this case).

#%%

#%% md
Finally, we also note that `.map()` also accepts a `callable` that will be applied element-wise to the `Series` (operates on one row at a time). The `callable` can either be an anonymous `lambda` function or a named functions (defined with the `def` keyword).

#%%

#%%

#%%

#%% md
In summary, `.replace()` and map _can_ do similar things. `.map()` is more generic: It can be used to replace values in a `Series`, but at the same time it does more than just replace specified values. Unspecified values will be replaced as well (e.g. with `NaN`) and, when passing a `callable`, we can specify operations to be applied to every element of the `Series`. As a result the `dtype` of the resulting `Series` can be different from the of `Series` on which `.map()` has been called on. This makes `.map()` more difficult to reason about and a more detailed inspection of the code may be required to fully understand the intent of calling it.

#%% md
### Quiz

#%% md
<span style="display:none" id="1_SeriesOperations:0.0" class="1_SeriesOperations:0.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGlzIGEgdmFsaWQgYm9vbGVuIGV4cHJlc3Npb24gd2hlbiBgYWAgYW5kIGBiYCBhcmUgTnVtUHkgYXJyYXlzPyIsICJhbnN3ZXJzIjogW3siY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogIihhICUgMiA9PSAwKSAmIChiIDwgNDIpIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogImEgLSBiIDwgMCJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAiKGEgJSAyID09IDEpIGFuZCAoYiAqIGEgPT0gbnAucGkpIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICIoYiA+IDApIG9yIChhIDwgMCkifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAiKGEgPiBiKSB8IChhIDwgYikifV19XQ==</span>
#%%
import jupyterquiz
jupyterquiz.display_quiz("#1_SeriesOperations:0.0")
#%% md
You are given a `Series` with name `s` with index labels `["a", "g", "h", "p", "q", "b", "t"]`.

#%% md
<span style="display:none" id="1_SeriesOperations:1.0" class="1_SeriesOperations:1.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGFyZSBwb3NzaWJsZSB3YXlzIHRvIGFjY2VzcyB0aGUgbGFiZWxzIGAnZydgLCBgJ2gnYCwgYCdwJ2AsIGAncSdgPyIsICJhbnN3ZXJzIjogW3siY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogInMubG9jW1tcImdcIiwgXCJoXCIsIFwicFwiLCBcInFcIl1dIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJzLmxvY1tcImdcIiwgXCJoXCIsIFwicFwiLCBcInFcIl0ifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAicy5sb2NbXCJnXCI6XCJxXCJdIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJzLmlsb2NbXCJnXCI6XCJxXCJdIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJzLmlsb2NbW1wiZ1wiLCBcImhcIiwgXCJwXCIsIFwicVwiXV0ifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAic1tbXCJnXCIsIFwiaFwiLCBcInBcIiwgXCJxXCJdXSJ9XX1d</span>
#%%

jupyterquiz.display_quiz("#1_SeriesOperations:1.0")
#%% md
<span style="display:none" id="1_SeriesOperations:2.0" class="1_SeriesOperations:2.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGFyZSB2YWxpZCB3YXlzIHRvIGdldCB0aGUgbWVhbiwgbWVkaWFuLCBhbmQgc3RhbmRhcmQgZGV2aWF0aW9uIGZyb20gYSBgU2VyaWVzYCBhbmQgcmV0dXJuIHRoZW0gaW4gYSBgU2VyaWVzYD8iLCAiYW5zd2VycyI6IFt7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJzLmFnZyhbXCJtZWFuXCIsIFwibWVkaWFuXCIsIFwic3RkXCJdKSJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJwZC5TZXJpZXMoW3MubWVhbigpLCBzLm1lZGlhbigpLCBzLnN0ZCgpXSkifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogInMubWVhbigpLCBzLm1lZGlhbigpLCBzLnN0ZCgpIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogInMuYWdnKHtcIm1lYW5cIjogbGFtYmRhIHg6IHgubWVhbigpLCBcIm1lZGlhblwiOiBsYW1iZGEgeDogeC5tZWRpYW4oKSwgXCJzdGRcIjogbGFtYmRhIHg6IHguc3RkKCl9KSJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAicy5yZXBsYWNlKHtcIm1lYW5cIjogbGFtYmRhIHg6IHgubWVhbigpLCBcIm1lZGlhblwiOiBsYW1iZGEgeDogeC5tZWRpYW4oKSwgXCJzdGRcIjogbGFtYmRhIHg6IHguc3RkKCl9KSJ9XX1d</span>
#%%

jupyterquiz.display_quiz("#1_SeriesOperations:2.0")
#%% md
You are given two `Series`, one with index `["a", "a", "b"]`, and the other with index `["a", "a", "d", "b"]`.

#%% md
<span style="display:none" id="1_SeriesOperations:3.0" class="1_SeriesOperations:3.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGlzIHRoZSBjb250ZW50IG9mIHRoZSBpbmRleCBmb3IgYSBgU2VyaWVzYCByZXN1bHRpbmcgZnJvbSBhbiBvcGVyYXRpb24gYmV0d2VlbiB0aGUgdHdvIChlLmcuIGFkZGl0aW9uKT8gKG9yZGVyIGRvZXMgbm90IG1hdHRlciBoZXJlISkiLCAiYW5zd2VycyI6IFt7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJbXCJhXCIsIFwiYVwiLCBcImFcIiwgXCJhXCIsIFwiYlwiLCBcImRcIl0ifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogIltcImFcIiwgXCJhXCIsIFwiYlwiLCBcImRcIl0ifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogIltcImFcIiwgXCJhXCIsIFwiYVwiLCBcImFcIiwgXCJiXCIsIFwiYlwiLCBcImRcIl0ifV19XQ==</span>
#%%

jupyterquiz.display_quiz("#1_SeriesOperations:3.0")
#%% md
<span style="display:none" id="1_SeriesOperations:4.0" class="1_SeriesOperations:4.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGljaCB2YWx1ZSBpcyB1c2VkIGFzIGEgcmVzdWx0IGluIHBvc2l0aW9ucyB3aGVyZSB0aGUgaW5kZXggb2YgdHdvIGBTZXJpZXNgIG9wZXJhbmRzIGRvIG5vdCBtYXRjaCBpbiBhbiBvcGVyYXRpb24/IiwgImFuc3dlcnMiOiBbeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAibmFuIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJpbmYifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogIi1pbmYifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogIk5vbmUifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogIjQyIn1dfV0=</span>
#%%

jupyterquiz.display_quiz("#1_SeriesOperations:4.0")
#%% md
### `.transform()` and `.apply()`

The [`.transform()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.transform.html) as well as [`.apply()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.apply.html) method are used to "apply" a function to the values of a `Series`. As we will soon see, `.transform()` more clearly conveys its intent --- which is "transforming" one `Series` into another --- while `.apply()` is more generic (and harder to understand).

We start with `.transform()`: It expects a `callable` that often is a user-defined (anonymous or named) function that operates on the whole `Series`. Some examples will best illustrate common usage of this method.

#%%
integers = pd.Series(data=range(10), index=[f"a{idx}" for idx in range(10)])
integers
#%% md
In our first example we query if a value is even or not. The result is a new `Series` of the same length with `dtype == bool` and the same entries in the index.

#%%

#%% md
Of course, the same result could have been achieved by writing a `bool`ean expression that directly uses the `Series` but often using `.transform()` is better as it nicely "paves the way" for calling multiple methods in sequence (method chaining).

#%%

#%% md
It is important to understand that the `.transform()` method expects a functions to actually "transform", i.e., reductions --- like summing all values --- are not allowed because this will _not_ return a `Series` but rather a single scalar value. Have a look at the following where we attempt to compute the mean of all values with the `.transform()` method.

```python
>>> integers.transform(np.mean)
...
ValueError: Function did not transform
```

#%%
# integers.transform(np.mean) # uncomment to get detailed output
#%% md
If the function used to transform the values has more than a single parameter, we can pass values for this parameter as keyword arguments to the `.transform()` method. The following functions shifts a value by a specified amount. We use it to scale all values to the interval $[0, 1]$.

$$
s_i^{(\mathrm{scaled})} = \frac{s_i - s_\mathrm{min}}{s_\mathrm{max} - s_\mathrm{min}}, \quad s_\mathrm{max} = \max_i s_i, \quad s_\mathrm{min} = \min_i s_i, \quad i = 1, \dots, N - 1
$$

#%%

#%%

#%% md
The same can result can be achieved by either using a `lambda` function or using [`functools.partial`](https://docs.python.org/3/library/functools.html#functools.partial).

#%%

#%% md
`functools.partial` will return a new function where some of its arguments have been fixed to concrete values. In our case we fix the `min_value` / `max_value` parameter to contain the smallest / largest value `integers`. The resulting function expects a single argument which is the element of the `Series` to transform.

#%%

#%% md
Finally, we also take note of the following cases. Firstly, we can pass on of NumPy's universal functions that operates on the whole `Series`.

#%%
integers.transform(np.square)
#%% md
Secondly, it is also possible to have a function that is called on one element at a time.

#%%
def f(x):
    print(type(x))  # inspect the type of the argument
    if x % 2 == 0:
        return "divisible by 2"
    elif x % 3 == 0:
        return "divisible by 3"
    elif x % 4 == 0:
        return "divisible by 4"
    else:
        return "something else"


(
    integers
    .iloc[:5]
    .transform(f)  # iloc for shorter output
)
#%% md
We briefly contrast this with the case in which the whole `Series` is the argument of the callable.

#%%
def f(s):
    output = (s - s.min()) / (s.max() - s.min())
    print(type(s))  # inspect the type of the argument
    return output


integers.transform(lambda s: f(s))
#%% md
We now turn to the `.apply()` method. Let's first look at an example where `.apply()` can be used in just the same manner as `.transform()`.

#%%

#%%

#%% md
So far, so good. We have seen that --- at least the case of transformations --- `.transform()` and `.apply()` can be used interchangably.

> note If you want to transform a `Series` (and later also `DataFrame`s) we recommend to use the `.transform()` method as it more clearly expresses the intent of what you actually want to achieve with this particular operation.

Let's revisit the case in which we earlier obtained a `ValueError: Function did not transform` with a slight modication of the the method call. When using `by_row=False` the function will be applied to the _whole_ `Series`. If the function happens to be a reduction, like computing the sum or the median of all elements, the result of the reduction is returned.

#%%

#%% md
The reduction above is equivalent to calling `.agg()` in the following way:

#%%

#%% md
In fact, we can mix transformations and reductions in a single call to `.apply()`. The transformation is the square operation and the addition, the reduction is computing the sum of the transformed result.

#%%

#%% md
A more readable way to write this is using method chaining:

#%%

#%% md
Yet another way to write the expression above is the following. We would, however, prefer the call to `.transform()` which allows to merge the two transformations in a single one and hence is more compact.

#%%

#%% md
> **note** Use `.transform()` if you want to transform and `.agg()` (or a specialized version like `.sum()` or `.mean()`) if you want to aggregate. While `.apply()` can do both, code using methods with explicit names more clearly convey your intent. The more complex your workflow, the more you will come to appreciate expressive code.

#%% md
### `.where()`

The `.where()` method is a useful tool to replace values based on a condition.

#%%
integers = pd.Series(range(10))
#%% md
In this example we test for all values < 5 but replace all values for which the condition `x < 5` is _not_ true with 0; i.e., all values >= 5 will be set to 0.

#%%

#%% md
Let's come up with a more realistic (still very contrived) example: We want to sum all values smaller than a certain threshold. Based on what we have learned so far we may indeed come up with multiple ways to achieve this.

The following figure sketches on possible way of doing this and also showcases the workflow of chaining method calls (method chaining).

![Chaining method calls on Pandas `Series` objects](img/SeriesMethodChaining-1.png)

#%%
threshold = 100
s = pd.Series(range(1_000))
#%% md
Set all values greater than `threshold` to 0 as they will not make a contribution to the sum.

#%%

#%% md
Return a `Series` containing only the values which are smaller than the threshold and sum them up.

#%%

#%% md
Like with `.where()` above, set all values greater than `threshold` to 0 as they will not make a contribution to the sum.

#%%

#%% md
Wrap the example using `.loc[]` from above with `.apply()`.

#%%

#%% md
### Interpolation

#%% md
* pandas can interpolate missing values
* various methods available
    * linear, splines, for smooth plotting, time-sensitive
* warning: this "invents" data
#%%

#%%

#%% md
## Exercises

#%%
rng = np.random.default_rng(seed=42)
#%% md
Suppose you have conducted an anonymous poll in which --- amongst other things --- you have asked participants to provide information regarding employment status. Annoyingly, the online form to gather the data contained a field in which people could write arbitray text (maybe the number of characters was limited) instead of a drop-down menu that provides several answers from which people can choose what fits them best. Anyway, as a result you are getting some answers which are not really suitable for your research (e.g. "Working with the Avengers"). You have to apply some of the techniques just learned about manipulating `Series` to bring the data into a form suitable for further processing.

To keep things simple let's assume the answers you were hoping for are `"Employed"` (or `"employed"` --- yes, capitalization can will also get in your way here ;-)), and `"Unemployed"` (or `"unemployed"`). We consider these as 'usable' while the rest is 'unusable' (we assume that the poll does not containe further information that allows to make them 'usable').

> **note** In this exercise you are meant to practise modifying the content of `Series` with suitable method calls. We strongly advise to use method chaining.

#%%
data = np.array(
    ["Employed"] * rng.choice(range(50, 200))
    + ["employed"] * rng.choice(range(10, 60))
    + ["Unemployed"] * rng.choice(range(10, 100))
    + ["unemployed"] * rng.choice(range(50, 70))
    + ["Working with the Avengers"] * rng.choice(range(2, 10))
    + ["Geht dich nix an"] * rng.choice(range(1, 5))
    + ["Rate mal"] * rng.choice(range(2, 8))
    + ["Having fun all day"] * rng.choice(range(5, 10))
    + ["geht dich nix an"] * rng.choice(range(10, 20))
)
rng.shuffle(data)

poll = pd.Series(data=data)
poll
#%% md
### Statistics

#%% md
How may instances of each category (every answer that has been given) is contained in the dataset? What is the proportion of each class in percent?

#%%

#%%

#%% md
What is the number 'usable' answers?

#%%

#%% md
What is the number of 'unusable' answers?

_Hint_: There are multiple free fields to fill in a solution because there are multiple ways to solve this.

#%%

#%%

#%% md
### Cleaning

#%% md
Now that you have an overview of how many usable and unusable answers you have gotten, it is time to bring the data into a cleaner form: Replace all entries you consider 'unusable' with `"unknown"`. Futher change all 'usable' entries to lowercase. Finally print the counts per (cleaned) category.

You are asked to solve this using 3 different methods (you will probably need other as well but the particular method should be used!).

#%% md
1. Use the [`.where()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.where.html) method.

#%%

#%% md
2. Use the [`.transform()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.transform.html) _or_ the [`.apply()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.apply.html) method.

#%%

#%% md
3. Use the [`.replace()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.replace.html) method.

#%%

#%% md
### Visualization

Generate two plots side-by-side that show _one_ of the following (you choose!):

- The counts of each category from the _uncleaned_ dataset on the left and the counts of each category in _cleaned_ dataset on the right.
- The relative proportions of the categories from the _uncleaned_ dataset on the left and the relative proportions from the uncleaned dataset on right.

Depending on what kind visualization form you use, choose an appropriate plot.

#%%

#%%

#%% md

#%% md

#%% md

#%% md
