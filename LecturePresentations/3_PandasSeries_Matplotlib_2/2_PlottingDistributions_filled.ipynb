#%%
%matplotlib inline

# Required packages
import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt

f'{pd.__version__=}, {np.__version__=}'
#%% md
# Visualizing distributions

The process of gathering data usually means sampling from an underlying distribution. Take for example how net income is distributed amongst citizens of a country. Very roughly speaking, there will be people with low, medium and high net income. However, the number of people with low income will not the same as that of people with medium or high income. The number of people were same for low, medium and high income we would say the data is uniformly distributed.

The data that we have will usally be continious, i.e. the data does not consist of categories like low, medium or high net income. We are rather given values from a certain range. Then it is quite often informative to visiualize the _empirical distribution_ of our given data.

> **note** Depending how the data was collected our empirical distribution might not closely resemble that actual distribution of the data. One of the reasons for this is usually only a finite amount of data samples are gathered.

#%%
rng = np.random.default_rng(seed=42)
#%%
s = pd.Series(rng.normal(loc=5, size=100))
#%% md
A common way to visualize such data is consider the range spanned by the smallest and largest value (min. and max. value) of the data. This range is subdivided into a given amount of _bins_. Each bin covers a certain portion of the range. Depending on its position in the range each data value will be assigned to a particular bin. Once all data values have been assigned, we _count_ all values contained in a bin. The position of each bin is then plotted on the x-axis and the count on the y-axis. Such a plot is called a _histogram_.

More formally: Let $a$, $b$ be the min. and max. value and let $N$ the number of bins. The width of each bin is given by $w = \operatorname{ceiling}\left((b - a) / N\right)$. The location of the $i$-th bin $B_i$ is $l_i = a + i\cdot w$, $i = 0, \dots, N - 1$. Some value $x_j$ ($j = 0, \dots, \vert D \vert - 1$, where $\vert D \vert$ is the number of data instances in some dataset $D$) is contained in the $i$-th bin if

$$
x_j \in B_i \Leftrightarrow l_i \leq x_j < l_{i + 1}.
$$

> It is also possible to use e.g. the _right_ boundary of the bin to test for equality: $x_j \in B_i \Leftrightarrow l_i < x_j {\color{red}{\;\leq\;}} l_{i + 1}$.

We can conveniently use the [`.plot.hist()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.Series.hist.html#pandas.Series.hist) method to generate such a histogram plot. The `bins` parameters can be used to determine the number of bins to used to collect data values.

#%%
(
    s.plot.hist(
        bins=20,
        title='Empirical Distribution of data',
        xlabel='value',
        ylabel='count',
    )
)
#%% md
Finally, we note that the we can also use a box plot to visualize a distribution. Such a plot deserves some further explanation, however.

### Interquartile range (IQR)

#### What is the IQR?

The IQR is the difference between the 25th (second quartile) and 75th (third quartile) percentile. Let $x$ be some feature; further let $q_{25}$​ and $q_{75}$​ be the 25th and 75th percentile for this feature. The IQR then is

$$
\Delta_\mathrm{IQR} = q_{75} - q_{25}
$$

The IQR can be visualised with the standard format of a boxplot.

- The upper and lower boundary of the box span the IQR which is bounded by the first ($q_{25}$) and the third quartile ($q_{75}$).
- The _median_ (second quartile, or $q_{50}$​) is the vertical bar inside the box.
- The vertical bars outside of IQR are at the positions $q_{25}​ - \frac{3}{2}​ \Delta_\mathrm{IQR}​$ (lower) and $q_{75} ​+ \frac{3}{2}​\Delta_\mathrm{IQR​}$ (upper).

:::{note} The distance of the vertical bars (also called _whiskers_) from the first and third quartile may be different even though they both should be $\frac{3}{2}​\Delta_\mathrm{IQR​}$​ away from the respective quantile. The reason is that the whisker can be at most $\frac{3}{2}​\Delta_\mathrm{IQR​}$​ away from the first or third quartile. If the smallest / largest value is closer than this distance from the first / third quartile, the the corresponding whisker must mark the position of this value instead. In this case the lower whisker marks the 0th percentile while the upper whisker marks the 100th percentile. It thus may happen that both whiskers have different distances from their quantile.
:::

The following sketch summarises some of the characteristics of a box plot.

![Box plot](img/BoxPlot-1.png)

#### What can the IQR be used for?

Based on the IQR and a distance of $\frac{3}{2}\Delta_\mathrm{IQR}$​ from the first and third quartile all points are considered "outliers" that have distances $> \frac{3}{2}\Delta_\mathrm{IQR}$​ from the corresponding quantile. Inside the boxplot the "outliers" are marked by the individual points.

#%%
(
    s.plot.box(
        title="Box plot of emirical data distribution",
        ylabel='value',
    )
)
#%% md
### Exercises

Below you are given some data sampled from a [Poisson distribution](https://en.wikipedia.org/wiki/Poisson_distribution).

#%%
s = pd.Series(rng.poisson(4, 20_000))
#%% md
#### Generate a histogram of the data

Make sure to add a title, and axis labels.

#%%
s.plot.hist()
#%% md
#### Percentiles

Compute the 0th, 25th, 50th (the median), 75th, and 100th percentile of the data by using a suitable Pandas method. Come up with at least two different approaches (methods) to accomplish this.

#%%
s.describe().loc[['min', '25%', 'mean', '75%', 'max']]
#%%
s.quantile([0, 0.25, 0.5, 0.75, 1])
#%% md
#### Box plot

Generate a box plot of the data. Make sure to add a title and axis labels where appropriate.

#%%
s.plot.box()
#%% md
Remove all "outliers" from the `Series` and return the remaining values as a new `Series`. Visualize the remaining data in a histogram. Make sure to use method chaining, to add a title and axis labels.

#%%
threshold = s.quantile(0.75) + 1.5 * (s.quantile(0.75) - s.quantile(0.25))
#%%
s.loc[s <= threshold].plot.hist(
    bins=7,
    title='Histogram with outliers removed',
    xlabel='value',
    ylabel='count'
)
#%% md
## Iris dataset

A plot often does not contain just a single kind of data (or a single line or a single type of points) but rather multiple (related) quantities are shown. Obviously, we need a way to distinguish them to be able to note important differences and commonalities.

Consider the previous example of the scatter plot. Assume we have data with differenet categories; for all categories certain quantities have been measured and we would like to compare the measurements between the categories. A commonly used example for such a dataset is the [Iris dataset](https://archive.ics.uci.edu/dataset/53/iris): The dataset contains several measurements for the petal width / length, and the sepal width / length of the Iris plant.

> _In German:_ petal: Blütenblatt, sepal: Kelchblatt.

#%% md
We start by downloading the Iris dataset:

- Download link: https://archive.ics.uci.edu/static/public/53/iris.zip
- DOI [10.24432/C56C76](https://doi.org/10.24432/C56C76)
- Dataset creator: R. A. Fisher
- License: [CC-BY-4.0](https://creativecommons.org/licenses/by/4.0/legalcode)

#%%
from pathlib import Path


def get_iris(url: str, output_path: Path):
    def download_and_extract(
        url: str,
        output_path: Path = Path("_iris"),
    ) -> None:
        import io
        import requests
        from zipfile import ZipFile

        output_path.mkdir(exist_ok=True, parents=True)

        (ZipFile(io.BytesIO(requests.get(url).content)).extractall(path=output_path))

    download_and_extract(url, output_path)

    with open(output_path / "iris.data", "r", encoding="utf-8") as f:
        content = [line.split(",") for line in f if line != "\n"]

        quantities = [
            "sepal length",
            "sepal width",
            "petal length",
            "petal width",
        ]

        measurements, labels = (
            np.array([[float(x) for x in line[:-1]] for line in content if line]),
            np.array([line[-1].rstrip() for line in content]),
        )

    return quantities, measurements, labels
#%% md
The data is returned as follows:

- `quantities` - `list`of names of the measured quantities: sepal length, sepal width, petal length, petal width (all given in cm units).
- `measurements` - nested `np.ndarray` containing the measured values. columns 0: sepal lengths, column 1: sepal width, column 2: petal length, and column 3: petal width.
- `labels`: Kind of Iris plant: Measurements have been conducted for the species Iris setosa, Iris versicolor, and Iris virginica. For each row in `measurements` there is a label which are store in a one-dimensional `np.ndarray`.

#%%
quantities, measurements, labels = get_iris(
    "https://archive.ics.uci.edu/static/public/53/iris.zip",
    Path("_iris"),
)
#%% md
As the dataset contains measurements for continuous quantities (widths and lengths) we first ask for the distribution of the sepal length / width, and petal length / width depending on the species. We plot the distribution of these quantities as histograms.

For each measured quantity we include three histogram plot in one graph, one for each species. In this case it is important to add a legend to be able to correctly assign each histogram to one of the species. We choose a columnar layout and we make all graphs share the same scale on the x-axis. This allows to compare the range of values covered by the measured quantities. For example, we can easilty see that the petal width is smaller than the petal length for each species. At the same time we observe that the distribution of values for both quantities tends to be quite narrow for the Iris setosa species while it is broader for Iris-versicolor and Iris-virginica.

#%%
fig, axes = plt.subplot_mosaic(
    np.array(quantities).reshape((-1, 1)),
    layout="constrained",
    sharex=True,
    figsize=(10, 8),
)

fig.suptitle("Histograms of measured lengths and width of Iris dataset")

for idx, (quantity, ax) in enumerate(axes.items()):
    for name in np.unique(labels):
        ax.hist(
            measurements[labels == name, idx],
            histtype="step",
            fill=False,
            linewidth=2.5,
            label=name,
        )
    ax.minorticks_on()
    # Use quantity as x-label and do not forget the units
    ax.set_xlabel(f"{quantity} / cm")
    ax.set_ylabel("count")
    # Use legend to be able to distinguish the histograms
    ax.legend()
#%% md
Next we ask for the correlation of between different quantities. These can be investigated with scatter plots. We conclude, for instance, that the petal length is _positively_ correlated with the petal width, as the latter is increasing when the former is. We might (be tempted to) conclude a slightly _negative_ correlation --- although not as clearly visible as for petal length and width --- between the sepal length and the sepal width.

> We tacitly ignore that this need not be the case for all species. A more in-depth analysis would investigate the correlation for the different species. Refer to the exercise below to see that we actually are mistaken regarding the negative correlation for sepal length and sepal width.

#%%
fig, axes = plt.subplots(4, 4, figsize=(12, 10))

# Scatter plots
for idx1, name1 in enumerate(quantities):
    for idx2, name2 in enumerate(quantities):
        ax = axes[idx1, idx2]
        ax.scatter(measurements[:, idx1], measurements[:, idx2])

# Axis labels
for (axv, axh), name in zip(zip(axes[:, 0], axes[-1, :]), quantities):
    axv.set_ylabel(f"{name} / cm")
    axh.set_xlabel(f"{name} / cm")
#%% md
### Exercise

For the Iris dataset create a scatter plot of petal length vs. petal width _and_ sepal length and sepal width. Disguish between the differenet species of the Iris plant, Iris-setosa, Iris-versicolor, and Iris-virginica in a suitable manner.

#%%
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 4))

for ax, (q1, q2) in zip(
    (ax1, ax2),
    (("petal length", "petal width"), ("sepal length", "sepal width")),
):
    idx1, idx2 = quantities.index(q1), quantities.index(q2)
    for name in np.unique(labels):
        ax.scatter(
            measurements[labels == name, idx1],
            measurements[labels == name, idx2],
            label=name,
        )
    ax.set_xlabel(f"{q1} / cm")
    ax.set_ylabel(f"{q2} / cm")
    ax.legend()