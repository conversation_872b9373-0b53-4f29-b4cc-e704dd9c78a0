#%% md
# Pandas `DataFrame` Objects

We start with a quote from the [Pandas documentation](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.html) for the `pd.DataFrame` object:

> Two-dimensional, size-mutable, potentially heterogeneous tabular data. [...] Can be thought of as a dict-like container for Series objects. The primary pandas data structure.

We will be honest with you... `DataFrame`s are _by far_ the more interesting and useful than `Series`. "Then why bother with `Series`?" you may ask. Well, we feel that knowing the basics well indeed is worthwhile. `Series` are an integral part of `DataFrame`s (more on this below) and so, sooner or later, we would have dealt with them anyway. Additionally, much of what we have learned about `Series` can be applied --- or transferred with ease --- to "`DataFrame` land". So, let's go right at it ...

To start with, take a look at the following sketch that tries its best to give an overview over the `DataFrame` object. As we can see a key difference between a `Series` and a `DataFrame` is that the latter can have multiple columns. These columns all share the same index and each of them is a fully-fledged `Series`. As a result a `DataFrame` is the go-to tool for handling datasets with multiple features. Usually, each feature will be a column. The columns may have names that describe the feature if possible (for some datasets we might not know what the features actually represent).

![](img/PandasDataFrame-1.png)

Before we dive into the details of `DataFrames` we have to make a few `import`s.

#%%
%matplotlib inline

import pandas as pd
import numpy as np
from matplotlib import pyplot as plt

f"Pandas version: {pd.__version__ = }, Numpy version: {np.__version__ = }"
#%% md
## `DataFrame` creation

As usual, we start with creating `DataFrames`. Since they are two-dimensional we can initialize them from nested data stuctures such a nested `list` or a `np.ndarray`.

#%%
pd.DataFrame([[1, 2, 3], [4, 5, 6]])
#%%
pd.DataFrame(np.arange(1, 7).reshape((2, 3)))
#%% md
While this for of initialization is straight forward, the result is not very useful. Obviously, we would (at least) like the columns to have some decent names. By default, i.e if no column labels are given, we are provided with integer values --- just as for the index. So, here we go and specify some names for the columns.

#%%
pd.DataFrame(np.arange(1, 7).reshape((2, 3)), columns=['a', 'b', 'c'])
#%% md
And, for the sake of completeness, we initialize the index as well.

#%%
pd.DataFrame(np.arange(1, 7).reshape((2, 3)), columns=['a', 'b', 'c'], index=['row 1', 'row 2'])
#%% md
Other ways to initialize a `DataFrame` are the following:

#%%
name = ["person 1", "person 2", "person 3"]
age = [23, 27, 34]
#%%
pd.DataFrame(data=zip(name, age), columns=['Name', 'Age'])
#%%
pd.DataFrame({'Name': name, 'Age': age})
#%% md
From the examples for creating a `DataFrames` you have been able to guess the names of the attributes.

> While this might be considered an anti pattern by some people, it is common to use variables names like `df` (short for dataframe) or `df_<something descriptive goes here>`.

#%%
df = pd.DataFrame({"Name": name, "Age": age})
#%%
df.index
#%%
df.shape
#%%
df.to_numpy()
#%% md
So far nothing new. However, we now also have `.dtypes` and `.columns` attributes where the latter that contains the names of all columns.

#%%
df.dtypes
#%%
df.columns
#%% md
We can obtain a compact overview of some important information when calling the [`.info()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.info.html) method.

#%%
df.info(memory_usage='deep'), df.info()
#%% md
Let's quickly revisit the call to the `.to_numpy()`method.

#%%
df.to_numpy()
#%% md
As you can see we receive a Numpy array with `dtype == object`. The reason for this is that the array must encompass the _data from all columns_ of the `DataFrame`. NumPy's real power lies in performant number crunching which requires numerical data types (some integer or some floating point data types). Since columns `DataFrame`s can evidently have different `dtype`s (cf. the content of `.dtypes`) packing all of them in a `ndarray` requires finding something like a "common deminator" for all types. If there is a column with `str` entries the `ndarray` cannot have a numerical type and we get `object`. Operations on arrays with numerical and non-numerical datatypes are not well-defined in all cases (after all, a `+` has a different meaning for `float` than for `str`). Additionally, operations with `dtype == object` arrays are not near as performant as with numerical `dtype`s. In short, calling `.to_values()` only makes sense if all columns have a numerical `dtype`. Even then, bear in mind that some types will be converted (like `int` to `float`) in the process of creating the `ndarrays`.

> In many datasets you will have numerical as well as non-numerical features. Machine learning algorithms usually require us to bring all the data into a numerical representation. Therefore, for such use cases, we usually _encode_ non-numerical data in a suitable manner (e.g., one-hot-encoding).

#%%
df = pd.DataFrame({"integers": [1, 2, 3], "floats": [10.0, 20.0, 30.0]})
df
#%%
df.dtypes
#%% md
All integers will be type-cast to `float64`.

#%%
df.to_numpy().dtype
#%% md
### Quiz

#%% md
<span style="display:none" id="1_DataFrames:0.0" class="1_DataFrames:0.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDMsICJxdWVzdGlvbiI6ICJXaGljaCBvZiB0aGUgZm9sbG93aW5nIGF0dHJpYnV0ZXMgZG8gUGFuZGFzIGBEYXRhRnJhbWVzYCBoYXZlPyIsICJhbnN3ZXJzIjogW3siY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICIuYXJyYXkifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogIi5yb3dzIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogIi5pbmRleCJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICIuY29sdW1ucyJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICIudmFsdWVzIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICIuZHR5cGUifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAiLnNpemUifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAiLnNoYXBlIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICIucm93cyJ9XX1d</span>

#%%
import jupyterquiz
jupyterquiz.display_quiz("#1_DataFrames:0.0")
#%% md
<span style="display:none" id="1_DataFrames:1.0" class="1_DataFrames:1.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJZb3UgYXJlIGdpdmVuIHR3byBgbmRhcnJheXNgIChzYW1lIHNpemUpIHdpdGggbmFtZXMgYHYxYCwgYW5kIGB2MmAuIFdoaWNoIG9mIHRoZSBmb2xsb3dpbmcgZXhwcmVzc2lvbnMgd2lsbCBjcmVhdGUgYSBgRGF0YUZyYW1lYD8iLCAiYW5zd2VycyI6IFt7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJwZC5EYXRhRnJhbWUoW3YxLCB2Ml0pIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogInBkLkRhdGFGcmFtZSh6aXAodjEsIHYyKSkifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogInBkLkRhdGFGcmFtZSh2MSwgdjIpIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJwZC5EYXRhRnJhbWUoe3YxLCB2Mn0pIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogInBkLkRhdGFGcmFtZSh7XCJjb2x1bW4gMVwiOiB2MSwgXCJjb2x1bW4gMlwiOiB2Mn0pIn1dfV0=</span>

#%%

jupyterquiz.display_quiz("#1_DataFrames:1.0")
#%% md
You are given the following `DataFrame` (as a table).

| Index |  A  |  B   |  C  |
| :---: | :-: | :--: | :-: |
|   a   |  1  | 40.0 | "a" |
|   b   |  2  | 30.0 | "b" |
|   c   |  3  | 20.0 | "c" |
|   d   |  4  | 10.0 | "d" |

#%% md
<span style="display:none" id="1_DataFrames:2.0" class="1_DataFrames:2.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGlzIHRoZSBgZHR5cGVgIG9idGFpbmVkIHdoZW4gY2FsbGluZyB0aGUgYC50b19udW1weSgpYCBtZXRob2Q/IiwgImFuc3dlcnMiOiBbeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogImZsb2F0NjQifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogImNvbXBsZXgxMjgifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogInVpbnQzMiJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJvYmplY3QifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogInN0ciJ9XX1d</span>

#%%

jupyterquiz.display_quiz("#1_DataFrames:2.0")
#%% md
### Exercises

#%% md
#### `DataFrame` creation 1

Given the `list` below generate a `DataFrame` in two different ways. The names of the columns shall be `"random numbers"` (for `values1`) and `"countdown"` (for `values2`).

#%%
values1 = np.random.randint(-10, 10, size=5)
values2 = range(5, 0, -1)
#%% md
First solution.

#%%
pd.DataFrame(
    columns=['random numbers', 'countdown'], data=zip(values1, values2),
)
#%% md
Second solution.

#%%
pd.DataFrame(
    data={'random numbers': values1,
         'countdown': values2}
)
#%% md
#### `DataFrame` creation 2

Generate a NumPy array with shape `(10, 10)` containing values 1, 2, ..., 100. Use this array to create a `DataFrame` with column names `"col1"`, `"col2"`, ..., `"col10"`, and index `"row1"`, `"row2"`, ..., `"row10"`.

#%%
pd.DataFrame(
    data=np.arange(1, 101).reshape((10, 10)),
    columns=[f'col{idx}' for idx in range(1, 11)],
    index=[f'row{idx}' for idx in range(1, 11)],
)
#%% md
## Accessing rows and columns

Now that we know about what makes a `DataFrame` its time to learn about how to access its content. Since there rows and columns, we can can access both individually.

To make things more interesting we will again work with the Iris dataset. Below we load the dataset from the [UCI Machine Learning Repository](https://archive.ics.uci.edu/) using the [`pd.read_csv()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html) function. This powerful function can load load CSV-like data from a large variety of sources (e.g http(s) or file URLs). For the moment, however, we ask to ignore the details of the function call as we will discuss using this function later in this course.

#%%
df_iris = pd.read_csv(
    "iris.data",
    names=["sepal length", "sepal width", "petal length", "petal width", "species"],
)
#%%
df_iris.sample(5)
#%% md
Let's first have look at the content of the `DataFrame`. The `.info()` method offers a compact overview thereof.

#%%
df_iris.info()
#%% md
The columns contain the measured values (`"[sepal/petal_[length|width]"`) in cm units. We note that each value has been stored as a 64-bit floating points value while the `"species"` column, that contains the name of a particular species for which a measurement has been made, is of type `object` (`str` values).

If we now wish to access columns with the Iris specie names we can do this in two different ways. On uses the `[...]` operator while the other uses the [`.loc[...]`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.loc.html) operator that we already now from our [earlier discussion](series-indexing-with-loc-and-iloc) of `Series`.

#%%
df_iris['species']
#%%
type(df_iris['species'])
#%%
df_iris.loc[:, 'species']
#%%
type(df_iris.loc[:, 'species'])
#%% md
Indeed, if we access a single column the result is a `Series` object. You will also have noted that the call to the `.loc[]` method is slightly different to what we are used from the `Series`. The differences is, that now we have the opportunity to index along the rows _and_ the columns. The syntax `.loc[:, "species"]` uses a slice `:` to access all rows in the column names "`species`". This is very much like we know it from `ndarray`s like in the following example:

#%%
a = np.array([[1, 2], [3, 4], [5, 6]])
a
#%%
a[:, 0]
#%% md
If we want more than just a single column --- say, all columns related to measurements of the sepal --- we can get their content by passing a `list` of valid column names to the `[...]` operator. Please note that that the order in which the columns are passed does _not_ matter. Indeed, we do not need to care too much about the order in which a `DataFrame` contains columns. If we provide a valid name the inner workings of such an object will make sure we get the right content (just like with an associative container as a `dict`).

> note While accessing a _single_ column returns a `Series` object, accessing multiple columns returns another `DataFrame` object.

The sketch below summarizes the differences between accessing a single column and multiple columns.

![Accessing a single row vs accessing multiple column](img/DataFrameColumnAccess-1.png)

#%%
df_iris[['sepal length', 'sepal width']]
#%%
df_iris[['sepal width', 'sepal length']]
#%% md
Again, we can also use the `.loc[]` method to get the same result. The way in which we specify the columns to be accessed is the same as with `[...]`.

#%%
df_iris.loc[:, ['sepal length', 'sepal width']]
#%% md
If the column names (partially) follow naming schemes that exhibit a certain pattern, the [`.filter()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.filter.html) method is helpful to select the columns according to a given pattern. If we say we want all columns containing the character sequence `"petal"` to can do this:

#%%
df_iris.filter(like='petal')
#%% md
We can also use regular expressions with `.filter()` which is particularly interesting if the column names are a bit more compilicated. For instance, in the following example we want to get all columns that end with `"length"` (this is what the `$` symbol means in the context of regular expressions).

#%%
df_iris.filter(regex="(length)$")
#%% md
Oftentimes, we want to select particular row and column entries at the same time. When selecting particular rows we have to query the index correspondingly as we have dealt with ealier in the section on [`Series`](series-indexing-with-loc-and-iloc). It is important to memorize that there is just a _single_ index for all columns in a `DataFrame`. That is, if we query the index we will get information from _all_ columns.

Consider the following example in which we query the index label `10` (in `df_iris` the index is just a row counter). The result is a `Series` where the index now consists of the column labels from `df_iris`.

#%%
df_iris.loc[10]
#%% md
Of course we can also use slicing. `DataFrame` also have a `.iloc[]` method available; regarding accessing the row dimension (`axis=:"rows"`) the semantics are the same as for `Series`. Have a look at the following to recall the different behaviour both methods. Note how the output from calling `.iloc[]` is missing the line with the index label `5`!

#%%
df_iris.iloc[0:5]
#%%
df_iris.loc[0:5]
#%% md
You will not be surprised that the following line is _equivalent_ to that using `.loc[]` two cells above. Through `:` we merely express the intent to use all the columns.

#%%
df_iris.loc[0:5, :]
#%% md
Now we can _combine_ row and column access like so:

#%%
df_iris.loc[0:5, "sepal length"]
#%% md
This gives a `Series` because we've only specified a single column name. Of course we can use more than just a single columns like we did before and get another `DataFrame`. The behavior is summarized in the following sketch.

![Accessing rows and columns with `.loc[]`](img/DataFrameColumnAccessLoc-1.png)

#%%
df_iris.loc[0:5, ["sepal length", "petal length"]]
#%% md
It may also get a bit fancier when using boolean masks for the index. For instance, let's say we want the columns `"petal length"` and `"sepal length"` from the `DataFrame` for the species `"Iris-setosa"`. _Note that we have limited the length of the output by chaining the call to `.loc[]` with [`.sample()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.sample.html) which randomly selects a certain number of rows_.

#%%
mask = df_iris['species'] == 'Iris-setosa'

df_sample = df_iris.loc[mask, ['petal length', 'sepal length']].sample(5)

df_sample.iloc[1], df_sample
#%% md
### Quiz

#%% md
<span style="display:none" id="1_DataFrames:3.0" class="1_DataFrames:3.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGRvZXMgdGhlIGV4cHJlc3Npb24gYGRmWydBJ11gIHJldHVybj8gYCdBJ2AgY2FuIGJlIGFzc3VtZWQgdG8gYmUgYSB2YWxpZCBjb2x1bW4gb2YgYGRmYC4iLCAiYW5zd2VycyI6IFt7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJBIFB5dGhvbiBsaXN0LiJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIkEgUGFuZGFzIFNlcmllcyJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJBbm90aGVyIE51bVB5IGFycmF5In0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIkFub3RoZXIgZGF0YWZyYW1lIn1dfV0=</span>

#%%

jupyterquiz.display_quiz("#1_DataFrames:3.0")
#%% md
<span style="display:none" id="1_DataFrames:4.0" class="1_DataFrames:4.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGlzIHRoZSB0cnVlIGZvciBmb2xsb3dpbmcgcGFpciBvZiBleHByZXNzaW9uczogYGRmWydBJ11gIGFuZCBgZGZbWydBJywgJ0InXV1gPyBgJ0EnYCBhbmQgYCdCJ2AgY2FuIGJvdGggYmUgYXNzdW1lZCB0byBiZSB2YWxpZCBjb2x1bW5zIG9mIGBkZmAuIiwgImFuc3dlcnMiOiBbeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICJCb3RoIGFyZSB2YWxpZCBleHByZXNzaW9ucyJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJUaGUgbGVmdCBpcyBpbnZhbGlkIHdoaWxlIHRoZSByaWdodCBpcyB2YWxpZCJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJUaGUgbGVmdCBpcyB2YWxpZCB3aGlsZSB0aGUgcmlnaHQgaXMgaW52YWxpZCJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIlRoZSBsZWZ0IHJldHVybnMgYSBzZXJpZXMgd2hpbGUgdGhlIHJpZ2h0IHJldHVybnMgYSBkYXRhZnJhbWUifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiQm90aCByZXR1cm4gZGF0YWZyYW1lcyJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJCb3RoIHJldHVybiBzZXJpZXMifV19XQ==</span>

#%%

jupyterquiz.display_quiz("#1_DataFrames:4.0")
#%% md
<span style="display:none" id="1_DataFrames:5.0" class="1_DataFrames:5.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGlzIHRydWUgYWJvdXQgdGhlIGluZGV4PyIsICJhbnN3ZXJzIjogW3siY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIlRoZXJlIGlzIG5vIGluZGV4LiJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIlRoZSBpbmRleCBsYWJlbHMgY2FuIG9ubHkgYmUgYWNjZXNzZWQgd2l0aCB0aGUgYC5sb2NbXWAgbWV0aG9kLiJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIlRoZXJlIGlzIGEgc2luZ2xlIGluZGV4IGFuZCBpdCBpcyB1c2VkIHRvIGFjY2VzcyB0aGUgcm93IG9mIGVhY2ggY29sdW1uLiJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJUaGUgaW5kZXggbGFiZWxzIGNhbiBvbmx5IGJlIGFjY2Vzc2VkIHdpdGggdGhlIGAuaWxvY1tdYCBtZXRob2QuIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIlRoZXJlIGlzIGEgc2VwYXJhdGUgaW5kZXggZm9yIGVhY2ggY29sdW1uLiJ9XX1d</span>

#%%

jupyterquiz.display_quiz("#1_DataFrames:5.0")
#%% md
<span style="display:none" id="1_DataFrames:6.0" class="1_DataFrames:6.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGEgdmFsaWQgd2F5IHRvIGFjY2VzcyByb3cgYW5kIGNvbHVtbnMgYXQgdGhlIHNhbWUgdGltZT8gTG93ZXIgY2FzZSBsZXR0ZXIgYXJlIGluZGV4IGxhYmVscywgdXBwZXIgY2FzZSBsZXR0ZXJzIGFyZSBjb2x1bW4gbGFiZWxzLiIsICJhbnN3ZXJzIjogW3siY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiIiwgImNvZGUiOiAiZGYubG9jW1wiYVwiOlwiY1wiLCBbXCJBXCIsIFwiQlwiXV0ifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICIiLCAiY29kZSI6ICJkZi5sb2NbXCJhXCI6XCJjXCIsIFtcIkJcIiwgXCJBXCJdXSJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIiIsICJjb2RlIjogImRmLmxvY1tcImFcIjpcImNcIl1bW1wiQVwiLCBcIkJcIl1dIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiIsICJjb2RlIjogImRmW1wiQVwiLCBcIkJcIl0ubG9jW1wiYTpkXCJdIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIiIsICJjb2RlIjogImRmW1wiQVwiLCBcIkJcIiwgXCJhOmRcIl0ifV19XQ==</span>

#%%

jupyterquiz.display_quiz("#1_DataFrames:6.0")
#%% md
Feel free to experiment with following `DataFrame` to answer this question.

#%%
df = pd.DataFrame({"A": range(1, 5), "B": range(10, 50, 10)}, index=list("abcd"))
df

df[['A', 'B']]
#%% md
### Exercises

#%% md
#### Accessing multiple columns

Access the columns `"sepal length"`, `"petal width"` und `"species"` _at the same time_ in two different ways.

#%% md
First solution.

#%%

#%% md
Second solution.

#%%

#%% md
#### Get columns by pattern

Use the `.loc[]` method to select columns that contain the the substring `"width"` from the `df_iris` `DataFrame`.

> **note** No, the solution is not to just specify `["sepal width", "petal width"]` manually. Rather think about a programmatic way to generate such a `list`. It actually should be possible to just substitute `"width"` with `"length"` to get `["sepal length", "petal length"]`.
> :::

#%% md
Programmatically generate a `list` containing the relevant column names. Use this list with the `.loc[]` method.

#%%

#%% md
Programmatically generate a `bool`ean mask that has `True` entries at those positions where `df_iris.columns` has entries containing `"width"`. Use this boolean mask with the `.lop[]` method.

#%%

#%% md
#### Accessing rows and columns

For which species are the data instances in which the sepal length > 6 cm _and_ (at the same time!) petal length > 3.5 cm? How many instances are there per species. Use boolean masks and method chaining.

_Note_: The are multiple cells in which you can write a solution because there are several ways to solve this task.

#%%

#%%

#%%

#%% md
#### Replacing values

Assume the data for the specie `"Iris-setosa"` is not usable anymore. Change all entries containing _measured values_ (e.g. sepal length / width) in rows with `"Iris-setosa"` inside the `DataFrame`s `df_tmp1`, and `df_tmp2` to `np.nan`.

- `df_tmp1`: Use sequential indexing `df_tmp1[cols][rows]`. That is, first access all relevant columns with `[cols]` where `cols` is a placeholder for the corresponding columns. Secondly, access all relevent rows with `[rows]`.
- `df_tmp2`: Use indexing with the `.loc[]` method.

For both approaches check if the changes have come into effect.

#%%
df_tmp1 = df_iris.copy(deep=True)
df_tmp2 = df_iris.copy(deep=True)
#%% md
Sequential indexing for `df_tmp1`.

#%%
column_names = [cname for cname in df_tmp1.columns if cname != 'species']
df_tmp1[column_names][df_tmp1['species'] == 'Iris-setosa'] = np.nan
df_tmp1.sample(10)
#%% md
Use the `.loc[]` method for `df_tmp2`.

#%%
df_tmp2.loc[df_tmp2['species'] == 'Iris-setosa', column_names] = np.nan
df_tmp2.head()