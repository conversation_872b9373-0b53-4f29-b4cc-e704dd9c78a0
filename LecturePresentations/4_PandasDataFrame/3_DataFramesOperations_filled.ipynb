#%% md
# Operations on `DataFrame`s

As in the case of `Series` the Pandas `DataFrame` object has a large number of very useful methods. In the following we will study some of these and see how to use to extract information or how transform into a desired form. Through the technique of method chaining operations on `DataFrames` can be combined with the plotting functionalities which forms the basis of e.g. exploratory data analysis (EDA).

As usual, we start with some `import`s.
#%%
%matplotlib inline

import pandas as pd
import numpy as np
import matplotlib as mpl
from matplotlib import pyplot as plt

mpl.style.use("seaborn-v0_8-colorblind")

f"Pandas version: {pd.__version__ = }, Numpy version: {np.__version__ = }"
#%% md
In order to be coherent with previous sections we will again use the Iris dataset. But don't worry, we will deal with more interesting --- but also more complicated --- datasets soon.
#%%
df_iris = pd.read_csv(
    "https://archive.ics.uci.edu/ml/machine-learning-databases/iris/iris.data",
    names=["sepal length", "sepal width", "petal length", "petal width", "species"],
)
#%%
df_iris.info()
#%% md
## Statistics

The same methods we're already [familiar with](series-statistics) from the `Series` are of course also available for `DataFrame`s. There are, however, some subtle differences we have to highlight. While a `Series` just has a single "axis of operation" (along the rows) a `DataFrame` has *two*: the rows *and* the columns (this is the "new kid in town").

Let's start simple: We see what happens when we just call the [`.sum()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.sum.html) method on `df_iris`; but we will only sum the numerical data types. We also silently introduce another method with name [`.select_dtypes()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.select_dtypes.html) that allows to include / exclude columns according to their `dtype`. We leave it to you to explore the [documentation](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.select_dtypes.html) of this method.
#%%
(
    df_iris
        .select_dtypes('number')
        .sum()
)
#%% md
We get a `Series` with the original column name as index labels, i.e., the result is the same as taking each column separately and summing its content.
#%%
pd.Series(
    {name: df_iris[name].sum() for name in df_iris.columns.difference(['species'])}
)
#%% md
To make things more explicit we use the `axis` parameter to state that we want to sum along the rows. While it is also possible to specify `axis=0` we feel that it is more expressive to use `"rows"`. 
Additionally, we also limit the summation to the numeric `dtype`s by setting `numeric_only=True`.
#%%
df_iris.sum(
    numeric_only=True,
    axis='rows',
)
#%% md
Conversely, summing along the *columns* yields another `Series` with the same number of rows as the original `DataFrame`.
#%%
df_iris.sum(
    numeric_only=True,
    axis='columns',
)
#%% md
The following sketch summarizes the differences for summing (reducing) along the rows or columns.

![Reducing along the rows or columns](img/DataFrameReduceAxis-1.png)
#%% md
Other methods like 

* [`.mean()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.mean.html),
* [`.std()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.std.html)
* [`.var()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.var.html)
* [`.min()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.min.html)
* [`.max()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.max.html)

work analogously. 

Multiple statistics can be computed with the [`.agg()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.agg.html) method that we have already learned about when [discussing `Series`](series-statistics). Note how the index contains the names of the statistic functions while the column labels are those of the original `DataFrame`.
#%%
(
    df_iris.select_dtypes('number')
    .agg(['mean', 'median', 'std'])
)
#%% md
An interesting capability of this method is accept a dictionary that specifies a particular reduction per columns. This is best illustrated with an example: For the `"sepal length"` column we ask for the mean value, for the `"petal length"` column for the median values. Since there is just a single operation specified per column we get the results in a `Series`.
#%%
(
    df_iris.select_dtypes('number')
    .agg({
        'sepal length': 'mean',
        'petal length': 'std'
    })
)
#%% md
Even more curiously, we can even specify a `list` of operations to be applied to a column. The result are now contained in a `DataFrame`. The `NaN` values in the columns stem from the fact that the operations are not the same for both columns.
#%%
(
    df_iris.select_dtypes('number')
    .agg({
        'sepal length': ['mean', 'median'],
        'petal length': ['mean', 'std']
    })
)
#%% md
To finishe this section we mention the [`.describe()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.describe.html) method which gives a compact overview of basic statistics of a dataset.
#%%
df_iris.describe()
#%% md
### Quiz
#%% md
<span style="display:none" id="3_DataFramesOperations:7.0" class="3_DataFramesOperations:7.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGRvZXMgaXQgbWVhbiB0byBzdW0gYWxvbmcgYGF4aXMgPSAxYD8iLCAiYW5zd2VycyI6IFt7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJTdW0gYWxvbmcgdGhlIHJvd3MifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICJTdW0gYWxvbmcgdGhlIGNvbHVtbnMifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiU3VtIGFsb25nIHRoZSByb3dzIGFuZCB0aGUgY29sdW1ucyJ9XX1d</span>
#%%
import jupyterquiz
jupyterquiz.display_quiz("#3_DataFramesOperations:7.0")
#%% md
<span style="display:none" id="3_DataFramesOperations:8.0" class="3_DataFramesOperations:8.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGlzIHRydWUgZm9yIHRoZSBtZWRpYW4gYW5kIHRoZSA1MCUgcGVyY2VudGlsZT8iLCAiYW5zd2VycyI6IFt7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIlRoZXJlIGlzIG5vIGRpZmZlcmVuY2UsIHRoZXkgYXJlIHRoZSBzYW1lLiJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJUaGUgbWVkaWFuIGlzIGFsd2F5cyBsYXJnZXIgdGhhbiB0aGUgNTAlIHBlcmNlbnRpbGUuIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIlRoZSA1MCUgcGVyY2VudGlsZSBpcyBhbHdheXMgbGFyZ2VyIHRoYW4gdGhlIG1lZGlhbi4ifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICJCb3RoIGFyZSBhbHNvIGtub3duIGFzIHRoZSAybmQgcXVhcnRpbGUifV19XQ==</span>
#%%

jupyterquiz.display_quiz("#3_DataFramesOperations:8.0")
#%% md
<span style="display:none" id="3_DataFramesOperations:9.0" class="3_DataFramesOperations:9.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJHaXZlbiB0aGUgZnVsbCBgZGZfaXJpc2AgZnJvbSBhYm92ZS4gV2hpY2ggb2YgdGhlIGZvbGxvd2luZyBtZXRob2QgY2FsbHMgd2lsbCBub3QgdGhyb3cgYW4gZXJyb3IgKGEgYFR5cGVFcnJvcmAgdG8gYmUgcHJlY2lzZSk/IiwgImFuc3dlcnMiOiBbeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAiZGZfaXJpcy5zdW0oKSJ9LCB7ImNvcnJlY3QiOiB0cnVlLCAiYW5zd2VyIjogIiAiLCAiY29kZSI6ICJkZl9pcmlzLnN1bShudW1lcmljX29ubHk9VHJ1ZSwgYXhpcz1cImNvbHVtbnNcIikifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiICIsICJjb2RlIjogImRmX2lyaXMubWVhbihheGlzPVwicm93c1wiKSJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAiZGZfaXJpcy5hZ2coW1wibWVhblwiLCBcIm1lZGlhblwiXSkifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICIgIiwgImNvZGUiOiAiZGZfaXJpcy5kZXNjcmliZSgpIn1dfV0=</span>
#%%

jupyterquiz.display_quiz("#3_DataFramesOperations:9.0")
#%%
df_iris.mean(axis="rows", numeric_only=True)
#%% md
### Exercises
#%% md
#### Variance

Compute the variance by for all numeric data columns

1. by calling a suitable `DataFrame` method, and
2. by passing a suitable `DataFrame` to the corresponding NumPy function,

and store result in a `Series` that has the names of the numeric columns as index.

Compare the output and modify one of the calls if the results are *not* the same.
#%%
df_iris.var(numeric_only=True, axis='rows')
#%%
pd.Series(
    data = df_iris.select_dtypes('number').to_numpy().var(axis=0, ddof=1),
    index=df_iris.columns.difference(['species']),
)
#%% md
#### Percentiles

Come up with *two* different ways to compute the 25th, 50th, 75th and 100th percentile of each of the numeric columns.
#%%
df_iris.describe().loc[['25%', '50%', '75%', 'max']]
#%%
df_iris.select_dtypes('number').quantile([0.25, 0.5, 0.75, 1])
#%% md
#### Reductions

Compute the mean and the median value for the sepal length, sepal width, petal length, and petal width columns. Visualise the results appropriately. Try to compute both statistics with a *single* method call.

*Hints*:
* No, the "right" way to plot this it *not* a line plot! :-)
* To plot mean and median per measured quantity side-by-side it may help to [`.transpose()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.transpose.html).
#%%
df_iris.select_dtypes('number').agg(['mean', 'median']).transpose().plot.bar()
#%%

#%% md
## Operations

Now we deal with methods we already know from the section on manipulation `Series` objects. We discuss these in the context of `DataFrame`s but will also learn about some new methods as well.

The general principle of manipulating a `DataFrame` with a method call is shown in the following sketch.

![Manipulating a `DataFrame` with a method call](img/PandasDataFrameTransform-1.png)
#%% md
### `.apply()`

`.apply()` is an odd creature. Well will soon see that its return type *depends on the callable we pass as argument*. For some reason `.apply()` appears to be first method that comes into people's mind when you ask them about a method that can be used to manipulate a `DataFrame`. Even though, there are often much better methods. But anyway, let's see what this method has to offer.

We start by partly emulating `.agg()`.
#%%
df_iris.select_dtypes('number').agg('mean'), df_iris.select_dtypes(exclude='object').apply(lambda s: s.mean())
#%% md
That is not too surprising. We pass a reducing function that is applied to each columns (which is a `Series`) and get back a `Series` containing the result --- the same result would have been obtained with `df_iris.mean(numeric_only=True, axis="rows")` or `df_iris.select_dtypes("number").agg("mean")`.

Awkwardly enough, it is also possible to do the following: We make the `lambda` function return a `tuple` with results. We then get back a `DataFrame` where each row contains the results for a particular method call evaluated on all numerical columns of `df_iris`. Note that we [`.rename()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.rename.html) the index to better be able to compare the result with `.agg()` in the cell below.

> **note** If you have not seen an expression like that in the `.rename()` method be informed that this is known as a `dict`ionary comprehension (similar to a `list` comprehension but with the goal of creating a `dict`).

```python
>>> {idx: s for idx, s in enumerate(("a", "b", "c"))}
{0: 'a', 1: 'b', 2: 'c'}
```

This equivalent --- but much more compact and elegant --- to 

```python
>>> d = {}
>>> for idx, s in enumerate(("a", "b", "c")):
...     d[idx] = s
... 
>>> d
{0: 'a', 1: 'b', 2: 'c'}
```
:::
#%%
(
    df_iris.select_dtypes('number')
    .apply(lambda s: (s.mean(), s.std(), s.min(), s.max()))
    .rename(index={idx: name for idx, name in enumerate(('mean', 'std', 'min', 'max'))})
)
#%%
(
    df_iris.select_dtypes('number')
    .agg(('mean', 'std', 'min', 'max'))
)
#%% md
Remembering our discussion of `.apply()` with `Series` we recall that this method can also transform. Let's convert the units for the measured lengths and widths from cm to mm units.
#%%
df_iris.apply(
    lambda s: s * 10 if s.dtype != 'object' else s
)
#%% md
Indeed, we're doing a bit more here than what promised above. Taking a closer look at the `lambda` function we note the conditional return value. If the `dtype` of the column is `object` we return it as is; otherwise it must be a column with numerical `dtype` and we may safely multiply the by 10 (which converts from cm to mm units).

This is quite economical moment to show you how to convert a column's `dtype`. This is motivated by the fact that, when converted to mm units, the content of the sepal length, sepal width, petal length, and petal width columns can be represented as integers. Let's quickly convince ourselves that this is true:
#%%
(
    df_iris.select_dtypes('number')
    .mul(10)
    .apply(lambda s: (s - s.round()).pow(2).mean())
)
#%% md
Yes, I know this looks quite complicated but let us explain: First we grab all columns with numerical data type and multiply the whole `DataFrame` by 10 (conversion to mm units). Next we `.apply()` an reducing operation to each columns: We take each columns, subtract the columns with all entries rounded to the nearest integer value (`s.sub(s.round())`), then we square the difference (`.pow(2)`), and finally reduce to by computing the )mean (`.mean()`). Essentially, we have computed the *mean squared error* (MSE) for the each column $k$ of size $N$:

$$
\mathrm{SE}^{(k)} = \sum_{i = 0}^{N - 1} \left(s^{(k)}_i - \operatorname{round}\left(s^{(k)}_i\right)\right)^2
$$

Okay, so we can represent the values in the numeric columns as integers when they are in mm units. Then, how do we make the type conversion? We use the [`.astype()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.astype.html) method for this. We will use an unsigned 8 bit integer for this. 

* Why unsigned? Well, because lengths / widths should be strictly $\geq$0 and therefore we do not need a sign.
* Why just 8 bits? 8 bits are sufficient to represent numbers from 0 ... 255 (unsigned!). The largest values per column are ...
#%%
df_iris.select_dtypes('number').mul(10).max()
#%%
df_iris_mm = df_iris.astype({
    cname: 'uint8'
    for cname, dtype in df_iris.dtypes.items()
    if dtype != 'object'
})
#%%
df_iris_mm.info(), df_iris.info()
#%% md
Admittedly, we have gotten a bit off topic but I think we have learned some interesting an useful things along the way. Let's summarize this section with a warning.

> **warning** Mind what kind of functions you pass to the `.apply()` method. If the functions reduces, the result will be a `Series` (containing e.g. mean values); if the function transforms ---  i.e., it takes a column and returns a column of the same size but with modified content --- the result will be a `DataFrame`. Hence, `.apply()` fails to have clear semantics and in many cases resorting to methods like `.agg()` or `.transform()` (covered below) is a better option.
#%% md
### `.transform()`
#%% md
Recalling the previous section on `.apply()` we can ask what `.transform()` has to offer us. Well, for the most part the method is much clearer about what it mean to accomplish: Transforming a `DataFrame`'s content by operating on the columns.

> **note** Remember the discussion of this method in the context of `Series`: `.transform()` will complain if the function passed to it does *not* "transform" (e.g. if it reduces by computing the mean or something similar).

We repeat the conversion from cm to mm from before.
#%%

#%% md
As another application, let's center each column on its mean an scale it to have unit variance (also called [standardization](https://scikit-learn.org/stable/modules/generated/sklearn.preprocessing.StandardScaler.html)). We call the `.agg()` method right after to assure not to have made a mistake.
#%%

#%% md
For a better understanding of the operations, let's take apart the computational steps involved. What we want to implement is the following. We want to replace the $k$-th column of size $N - 1$ like so:

$$
s^{(k)}_i \to \frac{s^{(k)}_i - \operatorname{mean}\left(s^{(k)}\right)}{\operatorname{std}\left(s^{(k)}\right)}, \quad i = 0, \dots, N - 1
$$

1. From each column subtract the mean value: `s.sub(s.mean())`
2. Divide by the standard deviation: `.div(s.std())`

The whole expression could have also been written like 

```python
>>> (s - s.mean()) / s.std()
```

It often useful to visualize the results.

> **note** Due to scaling with the standard deviation the lengths and widths do *not* carry a unit anymore.
#%%

#%% md
> **note** I hope by now we actually have convinced you that method chaining indeed is cool. Very often, when working on a data science project, this will be part of your workflow --- in particular during EDA. "Massage" the data contained in your `DataFrame` by chaining suitable methods calls and then visualize it right away.

We close the discussion about `.transform()` by noting that is also possible to pass column-specific transformations in a `dict`. In the following we scale the sepal width by a factor of 10 and standardize the sepal length column.
#%%

#%% md
### Quiz
#%% md
<span style="display:none" id="3_DataFramesOperations:10.0" class="3_DataFramesOperations:10.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGNhbiBiZSBjb25zaWRlcmVkIHRydWUgZm9yIHRoZSBgLmFwcGx5KClgIG1ldGhvZC4iLCAiYW5zd2VycyI6IFt7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJJdCBjYW4gb25seSB0cmFuc2Zvcm0gY29sdW1ucyBhbmQgcmV0dXJuIGFub3RoZXIgYERhdGFGcmFtZWAuIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIkl0IGNhbiBvbmx5IHBlcmZvcm0gcmVkdWN0aW9ucy4gVGhlIHJlc3VsdCBhbHdheXMgaXMgYSBgU2VyaWVzYC4ifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICJJdCBjYW4gcGVyZm9ybSByZWR1Y3Rpb25zIGFuZCB0cmFuc2Zvcm1hdGlvbnMuIFRoZSB0eXBlIG9mIHRoZSBvdXRwdXQgZGVwZW5kcyBvbiB0aGUgY2FsbGFibGUgdGhhdCBpcyBwYXNzZWQuIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiVGhlIG1ldGhvZCBpcyB2ZXJ5IGdlbmVyaWMgYW5kIGxhY2tzIGNsZWFyIHNlbWFudGljcy4ifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICJUaGVyZSBvZnRlbiBtaWdodCBiZSBhIGJldHRlciBjaG9pY2UgdG8gYWNjb21wbGlzaCB0aGUgc2FtZSB0aGluZy4ifV19XQ==</span>
#%%

jupyterquiz.display_quiz("#3_DataFramesOperations:10.0")
#%% md
<span style="display:none" id="3_DataFramesOperations:11.0" class="3_DataFramesOperations:11.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGljaCBvZiB0aGUgZm9sbG93aW5nIHN0YXRlbWVudHMgYWJvdXQgYC50cmFuc2Zvcm0oKWAgYW5kIGAuYXBwbHkoKWAgYXJlIHdyb25nPyIsICJhbnN3ZXJzIjogW3siY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogImAudHJhbnNmb3JtKClgIG1ldGhvZCB3aWxsIGByYWlzZWAgYW4gZXhjZXB0aW9uIGlmIHRoZSBwYXNzZWQgY2FsbGFibGUgZG9lcyBub3QgdHJhbnNmb3JtIHdoaWxlIGAuYXBwbHlgIHdpbGwgbm90LiJ9LCB7ImNvcnJlY3QiOiBmYWxzZSwgImFuc3dlciI6ICJgLnRyYW5zZm9ybSgpYCBjbGVhcmx5IGV4cHJlc3NlcyBpdHMgcHVycG9zZSB3aGlsZSBgLmFwcGx5KClgIG1lcmVseSBzdWdnZXN0cyBpdCB3aWxsIGFwcGx5IGEgZnVuY3Rpb24gdG8gdGhlIGNvbHVtbnMuIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiYC50cmFuc2Zvcm0oKWAgY2Fubm90IG9wZXJhdGUgYWxvbmcgdGhlIGNvbHVtbnMgd2hpbGUgYC5hcHBseSgpYCBjYW4uIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiYC50cmFuc2Zvcm0oKWAgaXMgYSBkcm9wLWluIHJlcGxhY2VtZW50IGZvciBhbGwgcG9zc2libGUgdXNlLWNhc2VzIG9mIGAuYXBwbHkoKWAuIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiYC50cmFuc2Zvcm0oKWAgYXMgd2VsbCBhcyBhIGAuYXBwbHkoKWAgYXJlIGxpc3RlZCBhcyBkZXByZWNhdGVkIGluIHRoZSBQYW5kYXMgZG9jdW1lbnRhdGlvbi4ifV19XQ==</span>
#%%

jupyterquiz.display_quiz("#3_DataFramesOperations:11.0")
#%% md
You are given the following expression:

```python
>>> (
    df_iris.drop(columns=["species"])
    .transform({
        "petal length": lambda s: s.sub(s.mean()).div(s.std()),
        "petal width": lambda s: s.mul(10),
    })
)
```
#%% md
<span style="display:none" id="3_DataFramesOperations:12.0" class="3_DataFramesOperations:12.0">W3sidHlwZSI6ICJtYW55X2Nob2ljZSIsICJhbnN3ZXJfY29scyI6IDEsICJxdWVzdGlvbiI6ICJXaGF0IGlzIHRoZSByZXN1bHQgb2YgdGhpcyBleHByZXNzaW9uPyIsICJhbnN3ZXJzIjogW3siY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIlRoZSByZXN1bHQgaXMgYSBgU2VyaWVzYC4ifSwgeyJjb3JyZWN0IjogdHJ1ZSwgImFuc3dlciI6ICJUaGUgcmVzdWx0IGlzIGEgbmV3IGBEYXRhRnJhbWVgIHRoYXQgZG9lcyBub3Qgc2hhcmUgbWVtb3J5IHdpdGggdGhlIG9sZCBvbmUuIn0sIHsiY29ycmVjdCI6IHRydWUsICJhbnN3ZXIiOiAiVGhlIHJlc3VsdCBpcyBhIGBEYXRhRnJhbWVgIHRoYXQgaGFzIGNvbHVtbnMgd2l0aCBuYW1lcyAncGV0YWwgbGVuZ3RoJyBhbmQgJ3BldGFsIHdpZHRoJy4gVGhlIG90aGVyIGNvbHVtbnMgZnJvbSBgZGZfaXJpc2AgYXJlIG5vdCBhdmFpbGFibGUgYW55bW9yZS4ifSwgeyJjb3JyZWN0IjogZmFsc2UsICJhbnN3ZXIiOiAiVGhlIHJlc3VsdCBpcyBhIGBEYXRhRnJhbWVgIHRoYXQgaGFzIGNvbHVtbnMgd2l0aCBuYW1lcyAncGV0YWwgbGVuZ3RoJyBhbmQgJ3BldGFsIHdpZHRoJy4gVGhlIG90aGVyIGNvbHVtbnMgZnJvbSBgZGZfaXJpc2AgYXJlIHN0aWxsIGF2YWlsYWJsZSBpbiB0aGUgYERhdGFGcmFtZWAuIn0sIHsiY29ycmVjdCI6IGZhbHNlLCAiYW5zd2VyIjogIlRoZSByZXN1bHQgaXMgdGhlIG9sZCBgRGF0YUZyYW1lYCB3aGVyZSB0aGUgc3BlY2lmaWVkIGNvbHVtbnMgaGF2ZSBiZWVuIG92ZXJyaWRkZW4uIn1dfV0=</span>
#%%

jupyterquiz.display_quiz("#3_DataFramesOperations:12.0")
#%% md
### Exercises
#%% md
#### Removing columns

Come up with three  *different* ways to return a `DataFrame` from `df_iris` without the columns `"petal length"` and `"petal width"`.
#%%

#%%

#%%

#%% md
#### Rounding

Recall the situation above in which we tested if the measured values indeed can be represented as integers when converted to mm units.
#%%
(
    df_iris.select_dtypes("number")
    .mul(10)
    .apply(lambda s: s.sub(s.round()).pow(2).mean())
)
#%% md
Repeat the same computational steps but *without* the `.apply()` method (yes, using the `.transform()` method is also not allowed). Instead, operate on the *whole* `DataFrame`.

*Hint*
* Have a look at the [`.pipe()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.pipe.html) method. This method can be called with a `lambda` function.
#%%

#%% md
#### Operate along the columns with `.apply()`

The [`.apply()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.apply.html#pandas-dataframe-apply) method has an optional `axis` parameter that defaults to `axis = 0`, i.e., by default this method operates --- and this can be a reduction or a transformation --- along the *rows*. In this exercise you are asked to operate along the *columns*.

We revisit the task to standardizing the numeric columns of the Iris dataset. The procedure follows the equation

$$
s^{(k)}_i \to \frac{s^{(k)}_i - \operatorname{mean}\left(s^{(k)}\right)}{\operatorname{std}\left(s^{(k)}\right)}, \quad i = 0, \dots, N - 1.
$$

Above we have implemented this by means of the `.transform()` method. Now use `.apply()` and operate row-wise and check that the data actually is normalized. Afterward visualize the results as boxplots.

*Hint*

It may to help to compute the mean and the standard deviation for each numeric column first.
#%% md
Standardize and 
#%%

#%% md
Visualize the results as boxplots.
#%%

#%% md
#### Adding new columns 1

For this exercise we need working copy.
#%%
df_iris_work = df_iris.copy(deep=True)
#%% md
We know how to access a single column of a `DataFrame`.
#%%
df_iris_work["species"].sample(10)
#%% md

Indeed, this syntax can be used to *add* new columns to a `DataFrame`:

```python
>>> df_iris_["name of some new column"] = ... # expression that returns a `Series`
```

> **warning** The `Series` to be stored in the new column must have the same length as the other columns in the `DataFrame`.

> **warning** Below we will learn about a better method to add new columns to a `DataFrame`. The issue with the current method is that we tend to "clutter" our (original?) `DataFrame` with columns we might *not* want to keep all the time. Indeed, often (not always, though) new columns are *intermediate* results we need e.g. for some particular visualization. Therefore, it is not worthwhile keeping the column(s) around after we're done with that part of our analysis.

Add new columns with names `"sepal ratio"` and `"petal ratio"`. Each of these columns contains the ratio of the measurements of the widths and lengths for sepals and petals, respectively. Afterwards plot the `"sepal ratio"` against the `"petal ratio"` in a scatter plot. Distinguish the different species setosa, versicolor, and virginica with different colors.
#%%

#%%

#%% md
#### Adding new columns 2

Solve the task from the previous exercise using the [`.assign()`](https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.assign.html) method.
#%%

#%% md
#### Filtering rows

Retrieve all rows from `df_iris` that contain the entry `"Iris-setosa'"` in the `"species"` column. Obtain the rows in two different ways. Additionally compute some basics statistics for this subset of data (e.g. mean, std, median, ...).

1. Make the `"species"` column the index and select the corresponding rows. Inspect the [`DataFrame` documentation](https://pandas.pydata.org/pandas-docs/stable/reference/frame.html) for suitable methods.
#%%

#%% md
2. Use the [`.query()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.query.html) method.
#%%

#%% md
## Grouping

Oftentimes items in a dataset can be *grouped* in a certain manner (e.g., if a column contains a value multiple times). The Iris dataset, for instance, can be grouped according the species of each flower. This allows to easily extract group-related information.

Pandas `DataFrame`s provide the [`.groupby()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.groupby.html) method to group a `DataFrame` with respect to one or multiple columns. 

```python
>>> df.groupby(by=["<column label>"])
```

The `DataFrame` is split and entries (rows) are grouped according to the values in the column with `"<column-label>"`. Once the data  has been grouped operations can be conducted on the items of each group. In this context it is important to note that grouping does *not* actually split the into new `DataFrame`s. That is, we do not end up with a bunch of new `DataFrame`s that consume additional memory. `.groupby()` returns an object of type `DataFrameGroupBy` that internally maintains all required information to establish the grouping (grouping is done "lazily"). It is important to understand that such an object essentially is a special *view* on the original `DataFrame`. No computations have been carried out when generating it (lazy evaluation).

The basic idea of grouping with respect to a single column is sketched in the following.

![Group `DataFrame` by single columns](img/PandasDataFrameGroupBy-1.png)

The Iris dataset is naturally grouped according to the `"species"` column.
#%%

#%% md
This data structure still knows about the columns that were present in the original `DataFrame`. We can use the `[<column-name>]` operation to access the columns with the corresponding label in each of the group members (subframes).
#%%

#%%

#%% md
`grouped_by_species` actually is iterable which we can use to get a glimpse under the hood.
#%%

#%% md
The subframes can be accessed directly with the [`.get_group("<a valid group name>")`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.core.groupby.DataFrameGroupBy.get_group.html#pandas.core.groupby.DataFrameGroupBy.get_group) method. The allowed names are the entries of the column according to which the data has been grouped. The method actually returns a `DataFrame`.
#%%

#%% md
Other methods for operating on grouped objects are documented [here](https://pandas.pydata.org/pandas-docs/stable/reference/groupby.html).
#%% md
One of the things that makes grouped objects so convenient is that we can still use many of the methods that we know from `Series` and `DataFrame`s. 
#%%

#%% md
By inspecting the result we see that the `.agg()` method is applied to all subframes (all groups). We can *think* of every operation passed to the aggregation method as returning a `Series` object containing the names of the groups as index; each of these `Series` constitutes a column in the resulting `DataFrame`. The method calls are called on the `DataFrameGroupBy` (or `SeriesGroupBy`) object but are *forwarded* to the subframes (or subseries). The following therefore is a valid method call.

#%%

#%% md
You will not be surprised that, starting from a grouped object, we can generate some interesting plots. For example, this chained call of several methods creates a plot that allows to nicely compare the mean value of lenghts and widths between all species (Iris setosa, Iris versicolor, and Iris viriginica). Note that we have [`.transpose()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.transpose.html)d the the `DataFrame` from above to get a plot where the measured quantities are on the x-axis. Otherwise, the names of the species would have been the labels on this axis.
#%%

#%% md
More or less as an aside, consider the following code. We use a [`.pivot_table()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.pivot_table.html) to reshape the original `DataFrame` in the same manner as with `.groupby()` above. While there are other important parameters, we limit ourselves to `columns` and `aggfunc` in this examples.

* `columns`: Specifies the columns from `df_iris` of which we want the unique entries to the be column labels of the resulting `DataFrame`. Internally, grouping happens with respect to this column. Values within each group are reduced (aggregated) with the specified `aggfunc`. In each of the remaining numeric columns (containing the sepal length, sepal width, petal length and petal width) groups of values are established according the entries in `"species"`; aggregation is carried out in each group in each column.
* `aggfunc`: Specifies the type of aggregation, .e.g. "sum", "mean", "std", or "median".
#%%

#%% md
For completeness, we create the same plot as above.
#%%

#%% md
Below we show a sketch of how a pivot table works (in Pandas).

![Pandas pivot table](img/PandasDataFramePivotTable-1.png)
#%% md
Here is the example from the sketch in code.
#%%

#%% md
### Exercises
#%% md
#### Pivoting

Modify the call to `.pivot_table()` from above in such a manner that the plot has the species on the x-axis. In more detail, the use `.pivot_table()` to generate the *transposed* version the `DataFrame` used to generate the plot above. And no, the solution is *not* to call `.transpose()` on the `DataFrame`. Rather, have a closer look at the [`.pivot_table()` documentation](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.pivot_table.html) and adapt the method call accordingly.

#%%

#%% md
#### Box plots

Use the `.groupby()` method to create boxplots of the spread of each measured quantity (sepal length, sepal width, petal length, and petal width). Create separate plot for each specie Iris setosa, Iris versicolor, and Iris virginica. The plots should share the same y-scale and have horizontal grid lines (y-axis).
#%%

#%% md
#### Standardization

Use the `.groupby()` method to apply standardization within each group belonging to a species. That implies that the group mean and standard deviation are used to shift and scale the values in each group.

The equation used to normalize each "feature" ($k \in $(sepal length, sepal width, petal length, petal width)) are repeated here for your convenience:

$$
s^{(k, G)}_i \to \frac{s^{(k, G)}_i - \operatorname{mean}\left(s^{(k, G)}\right)}{\operatorname{std}\left(s^{(k, G)}\right)}, \quad i = 0, \dots, N_G - 1,
$$

where $G$ is an additional index for the groups (which are Iris setosa, Iris versicolor, and Iris virginica).

Implement this group-based standardization  

1. using the [`.apply()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.core.groupby.DataFrameGroupBy.apply.html) method, and
2. using the [`.transform()`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.core.groupby.DataFrameGroupBy.transform.html) method.

In both cases very that the standarization yields the expected result with zero (per-group) mean value and unit (per-group) variance. Finally, visualize the standardized data in a suitable manner.
#%% md
Group-based standardization with `.transform()`.
#%%

#%% md
Group-based standardization with `.apply()`.
#%%

#%% md
Visualization of group-based standardization.
#%%
