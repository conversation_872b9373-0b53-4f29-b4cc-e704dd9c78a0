#%% md
# Naturwissenschaftliche Modelle
#%%
%matplotlib inline

import importlib

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

f"Pandas version: {pd.__version__}, NumPy version: {np.__version__}"
#%% md
## Was kann man unter einem Modell verstehen?
#%% md
![symbol picture modell](img/ChatGPT_modell.png)

Image created with ChatGPT
#%% md
- Natürliche Phänomene sind i.d.R zu komplex um sie in ihrer Gänze erfassen und darstellen zu können.
#%% md
- Mit einem Modell abstrahiert man die Realität und schafft einen Rahmen, innerhalb dessen sich die *wesentlichen Aspekte* eines Phänomens abbilden lassen. D.h. beim Prozess der Modellbildung werden oft wissentlich Vereinfachungen vorgenommen.
#%% md
- In den Naturwissenschaften werden Modelle meistens in Form von Gleichungen dargestellt. Diese sollen die wesentlichen Parameter enthalten, die zur quantitativen Beschreibung eines Phänomens notwendig sind.
- Durch Gleichungen lassen sich Phänomene formalisieren und die Konsequenzen des Prozesses der Modellierung berechnen.
- Die Berechenbarkeit eines Modells ist wesentliche Voraussetzung für die Überprüfung des Modells über einen Vergleich mit Experimenten.
#%% md
## Beschreibung von naturwissenschaftlichen Phänomenen
#%% md
![symbol picture apple](img/ChatGPT_apfel.png)

Image created with ChatGPT
#%% md
### Beobachtungen
#%% md
> Apfel fällt zu Boden. 
#%% md
> Je höher die Position von der der Apfel startet, desto größer ist seine Geschwindigkeit vor dem Aufprall auf dem Boden.
#%% md
**Schlussfolgerung**

Auf den Apfel wirkt eine Kraft, die ihn zu Boden fallen lässt. Diese Kraft beschleunigt den Apfel, sodass er während der Dauer des Falls immer schneller wird.
#%% md
**Idee**

Position des Apfels als Funktion des Zeit messen.
#%% md
In der Tat ist das der erste Schritt hin zu einer Formalisierung der Beobachtung. Die Aufzeichung der Details dieses Phänomens ist die Grundlage für jegliche theoretische (quantitative) Interpretation desselben.
#%%
def height_vs_time(h_start: float, t: np.ndarray, g: float = 9.81) -> np.ndarray:
    return h_start - g * t**2 / 2 
#%%
h_start = 50
time = np.linspace(0, 3, num=21)
height = height_vs_time(h_start, time) + np.random.normal(scale=0.5, size=time.size)

plt.xlabel(r"time / s")
plt.ylabel(r"height / m")
plt.grid()
plt.minorticks_on()
plt.plot(time, height, linestyle="", marker="o")
#%% md
### Theorie (physikalische Gesetzmäßigkeit)
#%% md
Die Beobachtungen, welche sich beim Fall eines Objektes aus einer bestimmten Höhe ergeben, lassen sich mit dem Gravitationsgesetz quantitativ beschreiben:
$$ F = - G \frac{m M_\mathrm{E}}{r^2} = m \frac{\mathrm{d}^2 r}{\mathrm{d} t^2} = m\cdot a$$
- Masse des fallenden Objektes: $m$
- Erdmasse: [$M_\mathrm{E} = 5.9722 \times 10^{24} \mathrm{kg}$](https://en.wikipedia.org/wiki/Earth_mass)
- Gravitationskonstante: [$G = 6.67430 \times 10^{-11}\mathrm{m^3 \cdot kg^{-1} \cdot s^{-2}}$](https://physics.nist.gov/cgi-bin/cuu/Value?bg)
- $r = R_\mathrm{E} + h$: $R_\mathrm{E}$ der Erdradius ist und $h$ die Höhe des Objektes über der Eroberfläche.
#%% md
Bei dieser Gleichung handelt es sich um den eine *Differentialgleichung 2. Ordnung*. Die Bestimmung der Bewegungsgleichung $h(t)$ für das fallende Objekt aus dieser Gleichung ist recht kompliziert.
#%% md
**Bemerkungen**

- Für die Entwicklung des Gravitationsgesetzes waren deutlich umfangreichere Beobachtungen notwendig als die über den (freien) Fall eines Apfels. Eine guten und recht unterhaltsamen Überblick darüber gibt Ihnen die [Vorlesung von Richard Feynman an der Cornell Universität](https://www.youtube.com/watch?v=j3mhkYbznBk&list=PLez3PPtnpncQLg_H7f6T9yJmJ2aCIBUHS) aus dem Jahre 1964.

- Selbst bei diesem Gesetz nehmen wir noch [Punktmassen](https://de.wikipedia.org/wiki/Massepunkt) an. D.h. wir behandelt die Objekte als unendlich keine Punkte der Masse $m$ und vernachlässigen dabei ihre räumliche Ausdehnung.
#%% md
### Modell
#%% md
Wir wollen uns bei unseren Betrachtungen auf Anfangshöhen beschränken, die klein im Vergleich zum Erdradius sind ($R_\mathrm{E} \approx 6378 \mathrm{km}$): $h \ll R_{E}$.

Mit der Näherung $R_\mathrm{E} + h \approx R_\mathrm{E}$ können wir nun obige DGL vereinfachen:

$$ - m \frac{G M_\mathrm{E}}{R_\mathrm{E}^2} = m\frac{\mathrm{d}^2}{\mathrm{d}t^2}(R_\mathrm{E} + h)$$

Wir führen noch folgende Abkürzung ein: $g = \frac{G M_\mathrm{E}}{R_\mathrm{E}^2} \approx 9.81 ~\mathrm{m}\cdot\mathrm{s}^{-2}$. Das ist die allseits bekannte (und auch beliebte?) *Erdbeschleunigung*.
#%% md
Die vereinfachte Bewegungsgleichung hat dann die finale Form:

$$ - m g = m\frac{\mathrm{d}^2h}{\mathrm{d}t^2} = m \frac{\mathrm{d}v}{\mathrm{d}t}$$
#%% md
Die resultierende DGL können wir nun in zwei Schritten mit den Anfangsbedingungen $v(t=0) = v_0 = 0 ~ \mathrm{m}\cdot \mathrm{s}^{-1}$ und $h(t=0) = h_0$ integieren:

**Geschwindigkeits-Zeit Gesetz**
$$
m \int\limits_{0}^{t} \frac{\mathrm{d}v}{\mathrm{d}t'} \mathrm{d}t' = - m g \int\limits_{0}^t \mathrm{d}t'
\Longleftrightarrow
m\left(v(t) - v(t=0)\right) = - m g t
\Longleftrightarrow
v(t) = - g t
$$

**Weg-Zeit Gesetz**
$$
\int\limits_{0}^{t} \frac{\mathrm{d}h}{\mathrm{d}t'} \mathrm{d}t' = - g \int\limits_{0}^{t} t' \mathrm{d} t'
\Longleftrightarrow
h(t) - h(t=0) = - \frac{1}{2}g t^2
\Longleftrightarrow
h(t) = h_0 - \frac{1}{2} g t^2
$$

#%% md
Quantitative Modelle werden für gewöhnlich in Form von mathematischen Gleichungen gefasst. Von diesen Modellen erwartet man, dass sie die beobachteten Phänomene in einem *ausreichenden* Umfang beschreiben.

In der Regel versucht man quantitative Modelle mit Worten zu beschreiben, um ihre Implikationen besser begreifen zu können.
#%% md
Das von uns bisher betrachtete Modell liefert nur innerhalb gewisser Grenzen eine quantitativ angemessene Beschreibung des Falls eines Objektes aus einer gewissen Höhe. Wir haben einige vereinfachende Annahmen gemacht, um den Ausdruck $h(t) = h_0 - \frac{1}{2} g t^2$ zu erhalten. 

U.a. sind dies:
- Annahmen kleiner Fallhöhen: $R_\mathrm{E} \gg h_0$ (Fall in der Nähe der Erdoberfläche).
- Vernachlässigung des Einflusses der Luftreibung.
#%% md
## Modelltypen
#%% md
### Deterministische Modelle

In *deterministischen* Modellen ist das Ergebnis des Modells vollständig durch die Parameters des Modells und die Angangsbedingungen bestimmt.
#%% md
**Bemerkung**

Wir haben mit dem Modell des Falls eines Objektes nach der Erdoberfläche bereits ein deterministisches Modell kennengelernt.
#%% md
### Stochastische Modelle
*Stochastische* Modelle sind inhärent vom Zufall anhängig. Derselbe Satz an Parameterwerten und Anfangsbedingungen liefert jeweils unterschiedliche Ergebnisse.
#%% md
### Weitere Unterscheidungen


- Klassifikationsmodelle
- Regressionsmodelle
- kontinuierliche Modelle
- diskrete Modelle
#%% md
#### Klassifizikationsmodelle

Ein Klassifikationsmodell ordnet Eingabedaten verschiedenen Kategorien zu. Dazu werden für i.d.R. Eigenschaften der Messpunkte aus dem Datensatz verwendet (z.B. Alter, Gewicht oder Koerpergroesse) um diese einzelnen Kategorien zuzuweisen.
#%% md
Beipiele für Klassifikationsaufgaben sind:
- Handelt es sich bei deiner E-Mail um Spam?
- Zuordnung handschriftlicher Symbole zu bekannten Symbolen aus einer Liste.
- Bestimmung der Arte ein Tumors: Boesartig oder gutartig?
#%% md
#### Regressionsmodelle

Regressionsmodelle werden verwendet um den Zusammenhang zwischen einer abhängigen Variable (z.B. einer Messgroesse) und einer (oder mehreren) unabhängigen Variablen zu beschreiben. Letztere sind die Groessen, die bei einer Messung variiert werden und in deren Abhängigkeit die Messgroesse aufgenommen wird.
#%% md
Regressionsmodelle haben folgende Bestandteile:
- Freie Parameter $\beta_j$ mit $j = 1, \dots, M$. Häufig werden diese in einem Vektor $\vec{\beta} \in \mathbb{R}^M$ zusammengefasst.
- Unabhängige Variablen $X_i$.
- Abhängige Variablen $Y_i$ (die eigentliche Messgroesse).
- Fehlerterme $\epsilon_i$. Diese sind zumeist nicht direkt aus den Daten ersichtlich und sind in der Messgroesse enthalten.

$$
Y_i = f(X_i, \vec{\beta}) + \epsilon_i, \qquad i = 1,\dots, N
$$
Dabei ist $N$ die Anzahl an Messwerten.
#%% md
Ziel der Regressionsanalyse ist die Bestimmung der freien Parameter $\vec{\beta}$. Sind diese Parameter bestimmt, koennen mit dem Regressionsmodell Aussagen über Daten getroffen werden, die *nicht* in dem Datensatz enthalten sind, der zur Bestimmung der freien Parameter verwendet wurde.
#%% md
Das einfachste Regressionsmodell ist die *lineare Regression*. In diesem Fall hat $f$ die Form
$$
f(X_i, \vec{\beta}) = \beta_0 + \beta_1 X_i 
$$
Hier sind $\beta_0$ der Ordinaten-Abschnitt und $\beta_1$ die Steigung.
#%% md
### Beispiel für ein stochastisches Modell: Geometrisches Bevölkerungswachstum
#%% md
Wir betrachten die Größe einer Population als Funktion der Zeit:

$N_t, N_{t+1}, N_{t + 2}, \dots$

Die Änderung $\Delta N$ der Populationsgröße zwischen zwei Zeitpunkte ergibt sich aus folgenden Einflüssen:

$$
\Delta N = N_{t + 1} - N_t = B - D + I - E
$$

- $B$, $D$: Geburten und Todesfälle
- $I$, $E$: Immigration und Emigration
#%% md
In einer abgeschlossenen Population gilt $I = E = 0$ und somit:

$N_{t+1} = N_t + B - D$

Nimmt man an, dass die Anzahl der Geburten und Todesfälle konstanten Raten folgen, $B = b N$ und $D = d N$ ($b, d \in  [0, 1)$), so kann man diese Gleichung wie folgt umformen:

$$
N_{t + 1} = N_t + N_t\left(\frac{B_t}{N_t} + \frac{D_t}{N_t}\right) = N_t + N_t(b_t - d_t) = N_t(1 + (b_t - d_t)) = \lambda_t N_t,
$$

wobei $\lambda_t = 1 + (b_t - d_t)$ ist.
#%% md
Wir stellen folgendes fest:

- $\lambda_t > 1$: Wachstum der Population
- $\lambda_t = 1$: Population stabil
- $\lambda_t < 1$: Abnahme der Population
#%% md
- Der das Wachstum bestimmende Faktor $\lambda_t$ folgt typischer Weise einer Wahrscheinlichkeitsverteilung. 
- Insbesondere handelt es sich bei dem Populationswachstum nicht um einen deterministischen Prozess sondern um einen stochastischen Prozess.
#%% md
Als Beispiel nehmen wir einen *diskreten stochastischen Prozess* an:
    
$$
\lambda_t \equiv \lambda = 
\begin{cases}
0.9~\text{mit}~p = 1 / 2\\
1.1~\text{mit}~p = 1 / 2
\end{cases}
$$
#%%
from typing import Callable, Generator


class Population:
    """Population growth based on a discrete stochastic model."""

    def __init__(self, N: int, model: Callable[[], float]) -> None:
        self.N: int = N
        self.model: Callable[[], float] = model

    def get_initial_size(self) -> int:
        return self.N

    def __iter__(self) -> Generator[int, None, None]:
        N_tmp: int = self.N
        while True:
            yield N_tmp
            N_tmp = int(N_tmp * self.model())
#%%
import itertools
import random

fig, axes = plt.subplots(1, 4, sharex="row", sharey="row", figsize=(20, 4))

N_step, initial_size = 100, 1000

axes[0].set_ylabel("population size")
for ax in axes:
    population = Population(N=initial_size, model=lambda: random.choice([0.9, 1.1]))
    ax.set_xlabel("time / arb. units")
    ax.plot(range(N_step), list(itertools.islice(population, N_step)))
    ax.axhline(y=initial_size, color="black", linestyle="--", linewidth=0.5)
#%% md
## Was muss ein naturwissenschaftliches Modell leisten?
Ein brauchbares wissenschaftliches Modell sollte ...
- ... wissenschaftliche Erklärung/Verständnis liefern.
- ... Vorhersagen treffen.
- ... Konsistenz über gesamten (oder zumindest einen grossen) Anwendungsbereich haben.
- ... Konsistenz zu anderen wissenschaftlichen Modellen haben.
- ... Nutzungskosten gering halten (im Vergleich zu anderen Modellen).
- ... Einfachheit besitzen. 