#%%
%matplotlib inline

import importlib

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

f"Pandas version: {pd.__version__}, NumPy version: {np.__version__}"
#%% md
# Genauigkeiten und Fehler
#%% md
## Vergleich verschiedener Modelle
#%% md
Die Geschwindigkeit eines Körpers der Masse $m$ im Fall *nahe der Erdoberfläche* lässt sich durch die folgende DGL modellieren:
$$
m\frac{\mathrm{d}v}{\mathrm{d}t}(t) = m g
$$
Dabei betrachten wir eine Bewegung nach unten, sodass die Geschwindigkeit das gleiche Vorzeichen hat wie die Gewichtskraft. 
#%% md
Die Lösung beschreibt eine gleichmässig beschleunigte Bewegung. Mit der Anfangsbedingung $v(t=0) = v_0 = 0 ~\mathrm{m}\cdot\mathrm{s}^{-1}$ erhalten wir:
$$
v(t) = g t
$$
#%% md
Bei einem Fall in Erdnähe nimmt die Geschwindigkeit eines Körpers der Masse $m$ mit einer gleichmäßigen Beschleunigung von $9.81~\mathrm{m}\cdot \mathrm{s}^{-1}$ zu: 
$$v(t) = g t$$ 
Nach diesem Modell steigt die Geschwindigkeit linear an bis der Körper auf dem Boden aufkommt.
#%% md
Die folgende Tabelle fasst einige Geschwindigkeiten nach einer bestimmten Fallzeit zusammen: 

| Zeit / $\mathrm{s}$ | Geschwindigkeit / $\mathrm{km}/\mathrm{h}$ |
|:--------:|:-----------------------------------------:|
|    15    |                    530                    |
|    30    |                    1059                   |
|    45    |                    1589                   |
|    60    |                    2119                   |
#%% md
In der Tat wirkt aber auf fallende Massen der Luftwiderstand. Die dadurch erzeugte Reibung hängt zu  jedem Zeitpunkt $t$ von der Geschwindigkeit des fallenden Körpers ab. Man unterscheidet zumeist

- Stokes-Reibung: $F_\mathrm{R} \propto v$
- Newton-Reibung: $F_\mathrm{R} \propto v^2$

Die Reibungskraft $F_\mathrm{R}$ wirkt dabei der Gewichtskraft entgegen.
#%% md
<figure>
<img src="https://upload.wikimedia.org/wikipedia/commons/e/e1/Freier_Fall_1.svg" width="300">
<figcaption>Bilderquelle: <a href="https://commons.wikimedia.org/wiki/File:Freier_Fall_1.svg">Kaneiderdaniel</a>, Public domain, via Wikimedia Commons</figcaption>
</figure>
#%% md
Wir modellieren den Einfluss der Reibungskraft auf den Fall eines Körpers der Masse $m$ mit Stokes-Reibung: $\lvert F_\mathrm{R} \rvert = \beta v$. Dabei ist $\beta$ der Reibungskoeffizient, welcher die Dimension $[\mathrm{kg}\cdot\mathrm{s}^{-1}]$ hat.
#%% md
Wir betrachten wiederum Bewegungen nach *unten*, sodass die Geschwindigkeit das gleiche Vorzeichen hat wie die Gewichtskraft. Dann erhalten wir die folgende Bewegungsgleichung:
$$
m \frac{\mathrm{d} v}{\mathrm{d} t}(t) = m g - \beta v(t)
$$
#%% md
Die Lösung dieser DGL 1. Ordnung ist recht kompliziert, sodass wir uns auf die Angabe der Lösung unter der Anfangsbedingung $v(t=0) = v_0 = 0~\mathrm{m}\cdot\mathrm{s}^{-1}$ beschränken:
$$
v(t) = \frac{m g}{\beta}\left(1 - \exp\left[-\frac{\beta t}{m}\right]\right)
$$
#%% md
Während die Geschwindigkeit in dem Modell ohne Luftreibung unbegrenzt ansteigt, 

$$
\lim_{t \to \infty} v(t) = \lim_{t \to \infty} g t = \infty
$$

hat die Geschwindigkeit im Falle der Beruecksichtigung von Luftreibung einen endlichen Grenzwert:

$$
\lim_{t \to \infty} v(t) = \lim_{t \to \infty} \frac{m g}{\beta}\left(1 - \exp\left[-\frac{\beta t}{m}\right]\right) = \frac{mg}{\beta}
$$
#%% md
Nun wollen wir untersuchen, wie stark sich die Lösungen beider Modelle für den Fall eines Körpers nahe der Erdoberfläche unterscheiden.

Dazu berechnen wir das Ergebnis der Lösungen beider Modelle und betrachten die Unterschiede in den Geschwindigkeiten.
#%%
# Some parameters to evaluate the model.
earth_acceleration: float = 9.81  # m / sec ** 2
friction_coefficient: float = 12.5  # kg / sec
body_mass: float = 68.1  # kg

tmin, tmax = 0, 30
times = np.linspace(
    tmin, tmax, num=31
)  # Times at which we want the solution to be evaluated.


def solution_without_friction(t):
    """Analytical solution of differential equation without friction."""
    return earth_acceleration * t


def solution_with_friction(t):
    """Analytical solution of differential equation with Stokes friction."""
    factor = body_mass / friction_coefficient
    return factor * earth_acceleration * (1 - np.exp(-t / factor))
#%%
# Helper function to plot solutions from both models.
def plot_models(figsize=(6, 6)):
    fig = plt.figure(figsize=figsize)
    plot_grid = plt.GridSpec(3, 1)
    ax_sol = fig.add_subplot(plot_grid[:2, 0])
    ax_error = fig.add_subplot(plot_grid[2, 0], sharex=ax_sol)

    ax_sol.grid()
    ax_sol.set_ylabel(r"velocity / km$\cdot$h$^{-1}$")
    ax_sol.plot(
        times,
        solution_without_friction(times) * 3.6,
        label="analytical solution (no friction)",
    )
    ax_sol.plot(
        times,
        solution_with_friction(times) * 3.6,
        label="analytical solution (Stokes friction)",
    )
    ax_sol.axhline(
        y=body_mass * earth_acceleration / friction_coefficient * 3.6,
        linestyle="--",
        color="black",
        linewidth=1,
        label="Limiting velocity",
    )
    ax_sol.legend()

    error = solution_with_friction(times) - solution_without_friction(
        times
    )  # difference in velocities
    error = np.abs(error)[1:] / solution_with_friction(times)[1:]
    ax_error.grid()
    ax_error.set_xlabel("time / s")
    ax_error.set_ylabel("relative error / %")
    ax_error.bar(times[1:], error * 100)


#     ax_error.plot(t_eval, np.cumsum(error))
#%%
# Compare solutions from both models.
# Error = solution with friction - solution without friction
plot_models((10, 10))
#%% md
Um zu verstehen, warum der Fehler für kleine Zeiten $t$ klein ist, betrachten wir die Lösung für das Modell mit Luftreibung:
$$
v(t) = \frac{mg}{\beta}\left(1 - \exp\left[-\frac{\beta t}{m}\right]\right)
$$
Für kleine $t$ kann man die Exponentialfunktion durch eine Potenzreihendarstellung (Taylor-Reihe) approximieren:
$$
e^{- a x} \approx 1 - a x
$$
Damit haben wir im Falle kleiner Zeiten die näherungsweise Lösung
$$
v(t) \approx \frac{mg}{\beta}\left(1 - \left[1 - \frac{\beta}{m}t\right]\right) = g t
$$
#%% md
- Für kleine Zeiten (zu Beginn des Fallprozesses) kann das Geschwindigkeits-Zeit-Gesetz also durch einen linearen Zusammenhang zwischen Erdbeschleunigung und Zeit beschrieben werden. 

- Dies ist aber gerade die Lösung für den Fall *ohne* Luftreibung. Daher ist die Abweichung der berechneten Geschwindigkeiten zu Beginn des Fallprozesses noch recht klein.
#%% md
Da die Geschwindigkeit des Körpers zu *Beginn* des Falls noch nicht so gross ist, ist die Abweichung zwischen den Lösungen beider Modelle nicht so gross. Das liegt daran, dass auf den Körper für kleine Zeiten annähernd nur die Erdbeschleunigung wirkt und die Reibung noch keinen allzu grossen Einfluss hat. Je groesser die Geschwindigkeit wird, desto mehr spielt der Reibungsterm eine Rolle ($F_\mathrm{R} \propto v$).
#%% md
## Numerische Genauigkeit
#%% md
In einigen Fällen ist die analytische Lösung von Gleichungen nicht mehr ohne weiteres moeglich. Wir können dann die Lösung nicht in Form eines mathematischen Ausdrucks mit einer endlichen Anzahl von Rechenoperationen angeben.
#%% md
In solchen Fällen muessen wir auf numerische Lösungsverfahren zurückgreifen. Solche Verfahren beruhen auf bestimmten Näherungen, die es uns erlauben, eine Lösung für unsere Gleichung zu berechnen. Typische Näherungen sind:

- Darstellung von Ableitungen über Differenzenquotienten.
- Darstellung von Integralen über Summen.
- Potenzreihenansatz für Lösungsfunktionen.
#%% md
### Numerische Lösung von Differentialgleichungen

#%% md
Eine sog. gewöhnliche Differentialgleichung (*ordinary differential equation* - ODE) hat die generische Form
$$
y'(x) = \frac{\mathrm{d}y}{\mathrm{d}x}(x) = f(x, y(x))
$$
Die Funktion $f$ auf der rechten Seite dieser Gleichung kann sowohl $y(x)$ (und damit $x$ *implizit*) als auch die Variable $x$  *explizit* beinhalten.
#%% md
*Beispiel*: Die ODE für den Fall mit Luftreibung kann in diese Form gebracht werden:
$$
\frac{\mathrm{d}v}{\mathrm{d}t}(t) = \underbrace{g - \frac{\beta}{m} v(t)}_{f(v(t))},
$$
also $f(v(t)) = g - \frac{\beta}{m} v(t)$. Hier hängt $f$ nur implizit von $t$ (über $v(t)$) ab.
#%% md
Für gewöhnlich interessieren wir uns für die Lösung innerhalb eines begrenzten Intervalls $[a, b]$. Für eine numerische Lösung der ODE wollen wir die Lösungsfunktion $y(x)$ an einer *endlichen* Zahl von Stützstellen $\{x_i\}_{i = 0,\dots,N}$ bestimmen (ingesamt $N + 1$ Punkte):
$$
a = x_0, x_0 + h, x_0 + 2h,\dots, x_0+(N-1)h, x_0 + N h = b
$$
Dabei ist $h$ die *Schrittweite* und ist gegeben durch 
$$h = \frac{b - a}{(N + 1) - 1} = \frac{b - a}{N}$$
#%% md
Zur Lösung der ODE integrieren wir nun beide Seiten über das Interval $[x_j, x_j + h]$:
$$
\int\limits_{x_j}^{x_j+h} y'(x)~\mathrm{d}x = \int\limits_{x_j}^{x_j + h} f(x, y(x))~\mathrm{d}x
\quad \Longleftrightarrow \quad
y(x_j + h) = y(x_j) + \int\limits_{x_j}^{x_j + h} f(x, y(x))~\mathrm{d}x.
$$
#%% md
#### Euler Verfahren

Für eine numerische näherungsweise Lösung müssen wir nun eine Näherung für den Term mit dem Integral finden. 

Eine naheliegende Möglichkeit bieten Variationen der [Rechtecksregel](https://de.wikipedia.org/wiki/Mittelpunktsregel):
$$
\int\limits_{x_j}^{x_j + h} f(x, y(x))~\mathrm{d}x \approx
\begin{cases}
h f(x_j, y(x_j))\qquad \text{linksseitige Rechtecksregel} \\
h f(x_j+h, y(x_j+h)) \qquad \text{rechtseitige Rechtecksregel}
\end{cases}
$$
#%% md
<figure>
    <img src="https://upload.wikimedia.org/wikipedia/commons/2/2a/Riemann_sum_convergence.png" width="600">
    <figcaption>
        <a href="https://commons.wikimedia.org/wiki/File:Riemann_sum_convergence.png">I, KSmrq</a>, 
        <a href="http://creativecommons.org/licenses/by-sa/3.0/">CC BY-SA 3.0</a>, via Wikimedia Commons
    </figcaption>
</figure>
#%% md
**Explizites Euler Verfahren**

Mit der *linksseitigen* Rechtecksregel erhalten wir einen expliziten Ausdruck für die den Wert der Lösungsfunktion an der Stelle $x_j + h$:
$$
y(x_j + h) \approx  y(x_j) + \underbrace{f(x_j, y(x_j))}_{=y'(x_j)} h.
$$
#%% md
**Implizites Euler Verfahren**

Mit der *rechtsseitigen* Rechtecksregel erhalten wir einen impliziten Ausdruck für den Wert der Lösungsfunktion an der Stelle $x_j + h$:
$$
y(x_j + h) \approx  y(x_j) + f(x_j+h, y(x_j+h)) h.
$$
#%% md
Bitte beachten Sie, dass auch die linke Seite dieser Gleichung den gesuchten Wert $y(x_j + h)$ enthält. In vielen Fällen können wir keinen geschlossenenen Ausdruck für $y(x_j + h)$ finden und muessen ein numerisches Verfahren für die Bestimmung der Nullstellen von $$F(\tilde{y}) = \tilde{y} - y(x_j) - f(x_j+h, \tilde{y}) h$$ (z.B. Newton Verfahren) verwenden. Die Nullstelle liefert dann die gesuchte Lösung für $y(x_j + h)$.
#%% md
#### Anwendung der Euler Verfahren

Wir verwenden nun die *explizite* und das *implizite* Eulerverfahren zur Lösung der ODE für den Fall mit Luftreibung. Dabei ist die Funktion $f$ auf der rechten Seite gegeben durch 
$$
f(v(t)) = g - \frac{\beta}{m} v(t)
$$
Diese Funktion ist nicht explizit von der Zeit abhängig sondern nur implizit über die Geschwindigkeit $v(t)$.
#%% md
**Explizites Euler Verfahren**
$$
v(t_j + \Delta t) = v(t_j) + f(v(t_j)) \Delta t
$$

**Implizites Euler Verfahren**
$$
v(t_j + \Delta t) = v(t_j) + f(v(t_j + \Delta t)) \Delta t
$$
#%%
# Some parameters to evaluate the model.
earth_acceleration: float = 9.81  # m / sec ** 2
friction_coefficient: float = 12.5  # kg / sec
body_mass: float = 68.1  # kg


def rhs(v):
    return earth_acceleration - friction_coefficient / body_mass * v


def solution_with_friction(t):
    """Analytical solution of differential equation with Stokes friction."""
    factor = body_mass / friction_coefficient
    return factor * earth_acceleration * (1 - np.exp(-t / factor))
#%%
from utils import solvers

importlib.reload(solvers)
#%%
tmin, tmax, tstep = 0, 35, 2

# Helper function to plot solutions from both models.
def plot_euler_variants(figsize=(6, 6)):
    fig = plt.figure(figsize=figsize)
    plot_grid = plt.GridSpec(3, 1)
    ax_sol = fig.add_subplot(plot_grid[:2, 0])
    ax_error = fig.add_subplot(plot_grid[2, 0], sharex=ax_sol)

    ax_sol.grid()
    ax_sol.set_ylabel("velocity / meters per second$")
    t_expl, v_expl = (
        solvers.EulerSolver((tmin, tmax), tstep, 0, rhs)
        .solve("euler-explicit")
        .to_array()
    )
    ax_sol.plot(t_expl, v_expl, "o", label="Euler explicit")
    t_impl, v_impl = (
        solvers.EulerSolver((tmin, tmax), tstep, 0, rhs)
        .solve("euler-implicit")
        .to_array()
    )
    ax_sol.plot(t_impl, v_impl, "s", label="Euler implicit")
    t_analyt = np.linspace(t_impl.min(), t_impl.max())
    v_analytical = solution_with_friction(t_analyt)
    ax_sol.plot(t_analyt, v_analytical, label="anayltical solution")
    ax_sol.legend()

    ax_error.grid()
    ax_error.set_xlabel("time / seconds")
    ax_error.set_ylabel("relative error / %")
    v_analytical = solution_with_friction(t_impl)
    error = v_analytical - v_expl  # difference in velocities
    error = error[1:] / v_analytical[1:]
    ax_error.bar(t_impl[1:], error * 100, label="Euler explicit")
    error = v_analytical - v_impl  # difference in velocities
    error = error[1:] / v_analytical[1:]
    ax_error.bar(t_impl[1:], error * 100, label="Euler implicit")
    ax_error.legend()
#%%
plot_euler_variants((10, 10))
#%% md
##  Gleitkommazahldarstellung
#%% md
Computer stellen Gleitkommazahlen mit endlicher Genauigkeit dar. Eine typische Darstellungsgenauigkeit ist 64 Bit (8 Byte) -- auch genannt "double precision". Dies ist die Standardgenauigkeit für `float` Objekte in Python und entspricht der Genauigkeit des C Typs `double`. Eine andere oft verwendete Genauigkeit von Gleitkommazahlen ist 32 Bit (4 Byte); dies entspricht der Genauigkeit des C Datentypes `float` (*Achtung*: Bitte nicht  mit dem Python `float` Datentyp verwechseln).

*Daumenregel*: Wissenschaftliche Berechnungen immer mit doppelter Genauigkeit (double precision) durchführen.
#%% md
### Machine `epsilon`
#%% md
Aufgrund der begrenzten Anzahl an Bits (z.B. 32 Bit oder 64 Bit), die für die Darstellung einer Gleitkommazahl in einem Computer verwendet werden, ist die Darstellungsgenauigkeit endlich. Das hat u.a. zur Folge, dass es eine kleinste positive Zahl $\epsilon_\mathrm{P}$ gibt, sodass 
$$
1 + \epsilon_\mathrm{P} > 1
$$
ist. 

D.h. bei dieser Zahl handelt es sich um die kleinst moegliche positive Zahl für die -- wenn sie zu 1 addiert wird -- ein Ergebnis groesser 1 erhalten wird. Diese Zahl hängt von der Genauigkeit der Darstellung $\mathrm{P}$ ab.
#%% md
Wir können diese Zahl (häufig auch *machine epsilon* genannt) in einem Computerexperiment ermitteln.
```
eps = 1.0

while (1 + 0.5 * eps) > 1:
    eps *= 0.5
```
Da Computer intern für Zahlen zur Basis 2 darstellen (Binärsystem), verringern wir `eps` jeweils immer durch Multiplikation mit dem Faktor `1/2`.
#%%
def find_eps(dtype=np.float64):
    """Find value such then when added to one yields one."""
    one = dtype(1)
    half = dtype(0.5)
    eps = eps_machine = one
    while one + eps > one:
        eps_machine = eps
        eps *= half
    return eps_machine, eps
#%%
[find_eps(dtype)[0] for dtype in (np.float16, np.float32, np.float64)], np.finfo(
    np.float64
).eps
#%% md
### Berechnungen mit verschiedenen Darstellungsgenauigkeiten
#%% md
Um Ihnen einen kleinen Überblick darüber zu geben, welchen Einfluss die Genauigkeit der Gleitkommadarstellung auf ein Rechenergebnis haben kann, berechnen wir folgende Summen:
- Aufwärts-Summation:
$$
S_\mathrm{up} = \sum_{n = 1}^N \frac{1}{n}
$$
- Abwärts-Summation:
$$
S_\mathrm{down} = \sum_{n = N}^1 \frac{1}{n}
$$

Bitte beachten Sie, dass im einen Fall von 1 bis $N$ *vorwärts* summieren und im anderen Fall von $N$ bis 1 *rückwärts* summieren.
#%%
def sum_forward(N, dtype=np.float64):
    sum_value = dtype(0)
    one = dtype(1)
    for n in range(1, N + 1):
        sum_value += one / dtype(n)
    return sum_value


def sum_backward(N, dtype=np.float64):
    sum_value = dtype(0)
    one = dtype(1)
    for n in range(N, 0, -1):
        sum_value += one / dtype(n)
    return sum_value
#%%
n_values = np.logspace(2, 7, dtype=int, num=6)
s = pd.Series(data=n_values, index=n_values)

fig, ax = plt.subplots(figsize=(10, 5))

ax.set_xlabel("$N$")
ax.set_ylabel("result of summation")
ax.set_xscale("log")
ax.plot(
    s.transform(lambda N: sum_forward(N, np.float32)), "o", label="forward (float32)"
)
ax.plot(
    s.transform(lambda N: sum_backward(N, np.float32)), "s", label="backward (float32)"
)
ax.plot(
    s.transform(lambda N: sum_forward(N, np.float16)), "o", label="forward (float16)"
)
ax.plot(
    s.transform(lambda N: sum_backward(N, np.float16)), "s", label="backward (float16)"
)
ax.legend()