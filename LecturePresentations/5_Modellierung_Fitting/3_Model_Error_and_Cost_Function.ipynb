#%%
%matplotlib inline

import importlib

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

f"Pandas version: {pd.__version__}, NumPy version: {np.__version__}"
#%% md
# Model Error and Cost Functions

In this lecture, we will...

1. introduce quantitative indicators for models
    - the residual
    - the cost function
2. define models with free parameters
    - why and when they're (not) useful
    - optimization
3. learn about and motivate typical cost functions
    - for regression tasks
    - for classification tasks
#%% md
## *Qualitative* Indicators
Recall: A sound scientific model...
- provides explanation/understanding
- makes *predictions*
- is consistent over its domain of application
- is consistent with underlying scientific theories and with other models
- is simple
#%% md
These indicators can differentiate between *kinds* of models:
- example: $h(t) = -1 - 0.15 t^2~~~~$ vs. $~~~~h(t) = 0.1 - t$
- what kind of situation are we modeling?
- how does our model derive from scientific laws?
- does the model respect all constraints?
#%%
def quadratic_model(t):
    return -1 - 0.15 * t**2
#%%
def linear_model(t):
    return 0.1 - t
#%%
from utils.utils1 import h, t, visualize_model
#%%
f, m = visualize_model(t, h, model=quadratic_model)
#%%
f, m = visualize_model(t, h, model=linear_model)
#%% md
##  *Quantitative* indicators

From here on, we will...
- assume we have $N$ observations of pairs $(x \in X, y \in Y)$

- denote the $i$th observation as $(x_i, y_i)$
  - independent (input) variable(s) $x_i$
  - dependent (output) variable(s) $y_i$

- denote the model as $m: X \to Y$
  - models are taken from a (potentially vast) space $M$ of possible models

- denote the model predictions as $\hat{y}_i = m(x_i)$
#%% md
### The residual
- difference between observed values and model prediction
  - $y_i - \hat{y}_i$
- parts of the data which the model does not explain:
  - neglected or approximated terms
  - random measurement error
  - data points outside the model domain or constraints
  - poorly fitted model
#%%
np.random.seed(1)
x = pd.Series(10 * np.random.rand(1000))
y = x.transform(lambda z: -z + np.random.randn())
#%%
figure = visualize_model(
    x, y, linear_model, plot_residual=True, residual_histogram=True
)
plt.show()
#%% md
## Free Parameters

- undetermined variables in a model
  - value is not predicted or constrained by underlying theory
  - must be estimated from the data

- free parameters need a *reason*
  - existence follows from underlying theory or constraints

- *kind* of relationship is known (ex. linear, quadratic), but *size* of effect is unknown
#%% md
### Example model: motion of a car
$$x(t) = x_0 + v t$$

- $x_0$ might be a fixed parameter
  - initial position relative to measurement apparatus
  - measured or derived independently
- $v$ might be a free parameter
  - reasoning: objects in motion will stay in motion (Newton's first law)
  - must be determined from data
  - gives otherwise inaccessible information
#%% md
gold standard in physics: models *without* free parameters
- only physical constants enter
- examples:
    - ab initio quantum chemistry methods
    - density functional theory
#%% md
> In desperation I asked Fermi whether he was not impressed by the agreement between our calculated numbers and his measured numbers. He replied, “How many arbitrary parameters did you use for your calculations?” I thought for a moment about our cut-off procedures and said, “Four.” He said, “I remember my friend Johnny von Neumann used to say, with four parameters I can fit an elephant, and with five I can make him wiggle his trunk.” With that, the conversation was over.

Dyson, F.: A meeting with Enrico Fermi. *Nature* **427**, 297 (2004). https://doi.org/10.1038/427297a
#%% md
## Training a model

- models can be *optimized*
  - choose parameters to minimize the cost function
  - depends on cost function
  - depends on observations
  - process is called *fitting* or *training* the model
#%% md
By optimization we obtain a model *without* free parameters which makes good predictions on unknown data.

- prerequisites:
    - a model $m$ with free parameters
    - observations $(x\in X, y \in Y)$
    - a cost function $J: M\times X \times Y \to \mathbb{R}$
#%% md
## The *Cost Function* ("Loss Function")
- quantify the error as a single scalar value
    - does the model describe the data well?
    - what is the perfect value of its parameters?
    - how can the model be *improved*?    
#%% md
- denote the cost function as $J$
  - maps model (with parameters) and observations to a real number
  - $J: M \times X \times Y \to \mathbb{R}$
  - many cost functions only need $\hat{Y}$ and $Y$
#%% md
## Typical cost functions

Training a model makes heavy use of the cost function supplied.

We will examine the process in-depth, but first we will take a look at common cost functions and their properties.
#%% md
### Linear Regression: Mean Squared Error

Suppose we have $N$ data points $(x_i, y_i)$, and a model $m: X \to Y$ with predictions $\hat{y}_i$.

We denote the *prediction* of the model at point $x_i$, which is $m(x_i)$, as $\hat{y}_i$. Then the mean squared error of that model is given by:
$$J(\hat{y}, y) = \frac{1}{N} \sum_{i=1}^N \left(y_i - \hat{y}_i\right)^2$$
#%%
figure, metrics = visualize_model(t, h, model=lambda x: -x - 0.1, errorbars=True)
print(';  '.join(f'{key}: {value}' for key, value in metrics.items()))
#%% md
- the mean error is useless
  - systematic errors at different points can cancel out
- the mean *absolute* error has problems
  - more than one solution
  - no analytical solution
- the mean squared error does the job
  - unique solution
  - desirable properties for training
#%% md
### Classification: Logistic regression

- dependent variable is *discrete* True/False (e.g. pass/fail, alive/dead, win/lose, ...)
- independent (input) variable(s) may be continuous
  - e.g. number of hours studied, blood loss, game score
- model needs to output a *probability* $p$ of getting True
  - strictly $p \in (0, 1)$: probabilities cannot be negative or greater than one
#%%
from utils.utils1 import x, y

visualize_model(x, y)
plt.show()
#%% md
- a linear model would map inputs to a scalar $\in \mathbb{R}$:
  - $q = a_0 + a_1 x$, as in linear regression

- The *logistic function* can map these to $(0, 1)$:
$$\hat{y} = \frac{1}{1 + \mathrm{e}^{-q}} = \frac{1}{1 + \mathrm{e}^{-(a_0 + a_1 x)}}$$
#%%
def logistic_function(q: float) -> float:
    """The logistic function maps real numbers to (0, 1)"""
    return 1 / (1 + np.exp(-q))


from utils.utils1 import plot_model

figure, axis = plt.subplots(figsize=(12, 6))
plot_model(axis, logistic_function, x_min=-6, x_max=6, label='logistic function')
plt.legend()
plt.show()
#%% md
#### Cost function for logistic regression: Average cross-entropy
  
relates (predicted) probability and (binary) observation
- "How much uncertainty remains?", colloquially: "$- \log (\Delta y)$"
- equivalently: $\begin{cases}
-\log(\hat{y}) & \text{if } y = 1\\
-\log(1-\hat{y}) & \text{if } y = 0\\
\end{cases}
$
- equivalently: $-(y \log(\hat{y}) + (1-y) \log(1-\hat{y}))$
#%%
figure, axis = plt.subplots(figsize=(10, 6))
plot_model(
    axis,
    model=lambda y: -np.log(y),
    x_min=0,
    x_max=1,
    label='loss when True',
    color='orange',
)
plot_model(
    axis,
    model=lambda y: -np.log(1 - y),
    x_min=0,
    x_max=1,
    label='loss when False',
    color='blue',
)
axis.set_xlabel('predicted value')
axis.set_ylabel('loss')
plt.legend()
plt.show()
#%% md
- average cross entropy then averages over all data points:
$$J(\hat{y}, y) = \frac{-1}{N} \sum_{i=1}^{N} y_i \log(\hat{y}_i) + (1 - y_i) \log(1 - \hat{y}_i)$$

- desirable properties:
  - 0 loss for correct classification
  - increases monotonically
  - without bounds
#%%
visualize_model(
    x=x, y=y, model=lambda x: 1 / (1 + np.exp(-0.65 * (x - 20))), errorbars=True
)
#%% md
"Why not mean squared error?"
- underpenalizes even strong misclassifications: Error is capped at 1
- not "convex" for logistic models

https://towardsdatascience.com/why-not-mse-as-a-loss-function-for-logistic-regression-589816b5e03c
#%% md
### Other examples
- hinge loss (for classification)
- Kullback-Leibler divergence (for probability distributions)
- regularization (to punish large or non-zero parameters)
#%% md
## Summary
- some models have free parameters
- quantitative indicators can then compare different values
- for linear regression: Mean Squared Error
- for classification: cross-entropy

Up next: Fitting
- how do we optimize a model, given data and a cost function?
#%% md
##  Tasks
#%% md
##### **1.**  
#%% md
Sie erhalten eindimensionale Daten in einem pandas DataFrame mit den Spalten `'x'` und `'y'` und ein Modell, welches aus den Einträgen $x_i$ der Spalte `'x'` Vorhersagen $\hat{y}_i$ generiert. 
#%%
data = pd.DataFrame({'x': np.random.rand(50), 'y': np.random.rand(50)})


def model(a_0: float, a_1: float) -> callable:
    return lambda x: a_0 + a_1 * x
#%% md
Schreiben Sie eine Funktion, die für dieses Modell und diese Daten das Residuum, die absoluten und quadratischen Fehler der einzelnen Datenpunkte berechnet. Die Funktion soll den übergebenen DataFrame mit den zusätzlichen Spalten `prediction`, `residual`, `absolute_error` und `squared_error` zurückgeben.
#%%
def calculate_errors(data: pd.DataFrame, model: callable) -> pd.DataFrame:
    data['prediction'] = data.x.transform(model)
    data['residual'] = data.y - data.prediction
    return data.assign(
        absolute_error=data.residual.abs(),
        squared_error=data.residual.transform('square'),
    )
#%% md
##### **2.**
#%% md
Um was für eine Art von Modell handelt es sich? Berechnen Sie mithilfe Ihrer Funktion das Residuum sowie den mittleren absoluten und mittleren quadratischen Fehler des Modells für verschiedene Werte der freien Parameter.
#%%
# Lineares Model
calculate_errors(data, model(.5,.5)).iloc[:, -3:].mean()
#%% md
##### **3.**
#%% md
Stellen Sie die Daten, das Modell, sowie das Residuum grafisch dar.
#%%
visualize_model(data["x"],data["y"],model=model(.5,.5))