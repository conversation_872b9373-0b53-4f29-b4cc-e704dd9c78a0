#%%
%matplotlib inline

import importlib

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

f"Pandas version: {pd.__version__}, NumPy version: {np.__version__}"
#%% md
# Linear regression with 1-dimensional models
#%% md
## Notation
- $\{x_i\} \equiv \{x_i\}_{i = 1, \dots, m}$: a set of $m$ independent variables
- $y_i$: dependent (target) variable
- $\hat{y}_i$: prediction of a model 
- $\boldsymbol{\beta}$: *column* vector of model parameters $\in \mathbb{R}^{m + 1}$
- $r_i = \hat{y}_i - y_i$: (negative) residual terms
#%% md
We have learned that *regression models* have the generic form
$$
\hat{y}_i = f(\{x_i\}; \boldsymbol{\beta}) = y_i + r_i
$$
#%% md
When using *linear regression* models we are concerned with the task of finding the unknown model parameters for a $f$ that is a linear function of the $\beta$s:
$$
f(\{x_i\};\boldsymbol{\beta}) = \beta_0 + \sum_{i=1}^{m} \beta_i h_i(x_i)
$$
#%% md
The functions $\{h_i(x)\}_{i = 1,  \dots, m }$ in general need *not* be linear functions of $x$.
#%% md
##  1-dimensional linear model
#%% md
Consider $N$ measurements yielding data points ${\{(x_i, y_i)}, i = 1\dots,N\}$. We further consider a 1-dimensional model. More precisely, we consider a model that only depends on $x_i$ ($m = 1$ and $h_1(x) = x$). 
#%% md
Then we have $\boldsymbol{\beta} \in \mathbb{R}^{1 + 1}$ and 
$$
f(x_i; \boldsymbol{\beta}) = \beta_0 + \beta_1 x_i, 
$$
and hence the relationship:
$$
\hat{y}_i \equiv \hat{y}(x_i; \boldsymbol{\beta})= \beta_0 + \beta_1 x_i, \quad i = 1, \dots, N.
$$
#%% md
The parameters to be determined are
- the intercept $\beta_0$, and
- the slope $\beta_1$.
#%% md
### Cost function
#%% md
We compute the (negative) *residual* as the difference between the predicted result and the actually measured result:
$$
r_i = \hat{y}_i - y_i.
$$
#%% md
The cost function $J: \mathbb{R}^{m + 1} \to \mathbb{R}$ is the mean of all squared resisuals ( $r_i \equiv r_i(\boldsymbol{\beta})$):
$$
J(\boldsymbol{\beta}) = \frac{1}{N}\sum\limits_{i=1}^{N} r_i^2
$$
#%% md
We note that the cost function is a [convex function](https://en.wikipedia.org/wiki/Convex_function).
#%% md
### Minimising the cost function
#%% md
We want to arrive at a model that best describes the measured data. "Best" in this case means that we want the model to have the smallest possible value for $J(\boldsymbol{\beta})$. 
#%% md
Therefore, we must adjust the unknown model parameters $\boldsymbol{\beta}$ in order minimise the $J$. In other words, we need to minimise $J$ with respect to the model parameters $\beta_j$.
#%% md
Mathematically we can express this in the following manner:
$$
\text{Find}~\min_{\{\beta_j\}_{j=0,\dots,m}} J(\boldsymbol{\beta}), 
\quad \text{where}~J(\boldsymbol{\beta}) = \frac{1}{N}\sum\limits_{i=1}^{N}\left(\beta_0 + \beta_1 x_i - y_i\right)^2,
$$
or equivalently
$$
(\tilde{\beta}_0, \tilde{\beta}_1) = 
\underset{\beta_0, \beta_1 \in \mathbb{R}}{\operatorname{argmin}} J(\boldsymbol{\beta}) = 
\underset{\beta_0, \beta_1 \in \mathbb{R}}{\operatorname{argmin}} \frac{1}{N}\sum\limits_{i=1}^{N}\left(\beta_0 + \beta_1 x_i - y_i\right)^2.
$$
#%% md
Since the cost function is convex if suffices to compute the gradient $\mathbf{g} = \nabla_{\boldsymbol{\beta}} J = \operatorname{grad}_{\boldsymbol{\beta}} J$ and to equate it to zero.
#%% md
The gradient is a vector $\mathbf{g} \in \mathbb{R}^{m + 1}$; its elements are the partial derivatives of $J(\boldsymbol{\beta})$ with respect to the the $\beta_j$s:
$$
g_j = 
\frac{\partial J(\beta_0, \beta_1, \dots,\beta_m)}{\partial \beta_j}
= \partial_{\beta_j}J(\beta_0, \beta_1, \dots,\beta_m)
$$
#%% md
In the particular case of out linear model we have for the partial derivatives:
#%% md
Derivative w.r.t $\beta_0$:
$$
\frac{\partial J}{\partial \beta_0} = 
\frac{2}{N}\sum\limits_{i=1}^{N}\left(\beta_0 + \beta_1 x_i - y_i\right)
$$
#%% md
Derivative w.r.t $\beta_1$:
$$
\frac{\partial J}{\partial \beta_1} = \frac{2}{N}\sum\limits_{i=1}^{N}x_i \left(\beta_0 + \beta_1 x_i - y_i\right)
$$
#%% md
Equating both equations to zero results in the [following set of equations](https://de.wikipedia.org/wiki/Lineare_Einfachregression):
#%% md
$$
\tilde{\beta}_1 = \frac{\sum\limits_{i = 1}^N(x_i - \bar{x})(y_i - \bar{y})}{\sum\limits_{i = 1}^N(x_i - \bar{x})^2}
$$
#%% md
$$
\tilde{\beta_0} = \bar{y} - \beta_1 \bar{x}
$$
#%% md
With: $\bar{x} = \frac{1}{N}\sum\limits_{i=1}^N x_i$ and $\bar{y} = \frac{1}{N}\sum\limits_{i=1}^N y_i$.
#%% md
If we have a dataset of size $N$ ($\{(x_i, y_i), i = 1,\dots,N\}$) and a model $\hat{y}(x; \boldsymbol{\beta})$ we can write down the equations for each $i = 1, \dots, N$. In this manner we get a linear system of equations. With our 1-dimensional model have:
#%% md
\begin{eqnarray}
\hat{y}_1 &= \beta_0 + \beta_1 x_1 &= y_1 + r_1\\
\hat{y}_2 &= \beta_0 + \beta_1 x_2 &= y_2 + r_2\\
\vdots\\
\hat{y}_N &= \beta_0 + \beta_1 x_N &= y_N + r_N
\end{eqnarray}
#%% md
This linear system of equations can more compactly be written in matrix vector form:
$$
\mathbf{X}\boldsymbol{\beta} - \mathbf{y} = \mathbf{r}
$$
#%% md
In more detail:

$\mathbf{X} = 
\begin{bmatrix}
1 & x_1 \\
1 & x_2 \\
\vdots & \vdots\\
1 & x_N\\
\end{bmatrix} \in \mathbb{R}^{N \times 2};
~~~~ \boldsymbol{\beta} = 
\begin{bmatrix}
\beta_0\\
\beta_1\\
\end{bmatrix} \in \mathbb{R}^2;
~~~~ \mathbf{y} = 
\begin{bmatrix} y_1\\y_2\\\vdots\\y_N\end{bmatrix} \in \mathbb{R}^N;
~~~~ \mathbf{r} = 
\begin{bmatrix}r_1\\ r_2 \\ \vdots \\r_N\\
\end{bmatrix}\in\mathbb{R}^N$
#%% md
In numerical linear algebra there exist methods to solve such (oftentimes overdetermined) linear systems of equations. The SciPy library implements such methods, e.g. in the  [`scipy.linalg.lstsq` function](https://docs.scipy.org/doc/scipy/reference/generated/scipy.linalg.lstsq.html).
#%% md
## Let's have an example
#%% md
### Set up some data to play with
#%%
xmin, xmax = 1, 5
N = 21

# Only needed to set up the data
slope, intercept = 2, 4

# For reproducibility fix the seed
rng = np.random.RandomState(42)

# values of the independent variable
x_measured = np.linspace(xmin, xmax, num=N)
# values of the dependent variable
y_measured = slope * x_measured + intercept + rng.normal(loc=0, scale=0.75, size=N)
#%%
# For better handling we put the data inside a `DataFrame`
df = pd.DataFrame(data={"x": x_measured, "y": y_measured})
#%% md
### Compute slope and intercept from the data
#%% md
We need the mean values of the independent variables and the dependent variables:

$$\bar{x} = \frac{1}{N}\sum\limits_{i=1}^N x_i$$ 
and 
$$\bar{y} = \frac{1}{N}\sum\limits_{i=1}^N y_i$$
#%%
# compute some helper quantities
df["x_centered"] = df["x"] - df["x"].mean()
df["y_centered"] = df["y"] - df["y"].mean()
#%% md
Recall the equation for the slope:
$$
\tilde{\beta_1} = \frac{\sum\limits_{i=1}^{N}(x_i - \bar{x})(y_i - \bar{y})}{\sum\limits_{i=1}^{N}(x_i - \bar{x})^2}
$$
#%%
# compute the slope and the intercept from the above equations
slope_from_regression = np.sum(df["x_centered"] * df["y_centered"]) / np.sum(
    df["x_centered"] ** 2
)
#%% md
Recall the equation for the intercept:
$$
\tilde{\beta}_0 = \bar{y} - \tilde{\beta}_1 \bar{x}
$$
#%%
intercept_from_regression = df["y"].mean() - slope_from_regression * df["x"].mean()
#%%
print(f"Slope = {slope_from_regression}; Intercept = {intercept_from_regression}")
#%%
# compute predictions and residual
df["y_pred"] = slope_from_regression * df["x"] + intercept_from_regression
df["residual"] = df["y_pred"] - df["y"]

df.head()
#%% md
### Compare the measurement and the predictions
#%%
# Let's compare the predictions and the measurement
_, ax = plt.subplots(figsize=(10, 6))
ax.set_xlabel("independent variable $x$")
ax.set_ylabel("dependent variable $y$")
ax.plot(df["x"].values, df["y"].values, "o", label="measurement", markersize=10)
ax.plot(
    df["x"].values,
    df["y_pred"].values,
    "-",
    label=r"linear regression: $\beta_0$=%.2f, $\beta_1$=%.2f"
    % (intercept_from_regression, slope_from_regression),
    linewidth=3,
)
ax.legend()
#%% md
#### Is there another option to compute the slope and the intercept?
#%% md
We can use libraries for this. Most prominently the `SciPy` library has some functions available for conduction regression tasks:

- [`scipy.stats.linregress`](https://docs.scipy.org/doc/scipy/reference/generated/scipy.stats.linregress.html)
    - Tailored towards linear regression.
- [`scipy.optmize.curve_fit`](https://docs.scipy.org/doc/scipy/reference/generated/scipy.optimize.curve_fit.html)
    - Implements *non-linear* least squares fitting of functions to data.
#%%
from scipy import optimize, stats
#%%
results = stats.linregress(df["x"].values, df["y"].values)
print(f"Slope = {results.slope}; Intercept = {results.intercept}")
slope_from_regression, intercept_from_regression
#%%
# Function to be passed to `curve_fit`. The order of arguments is important.
# 1st      argument: independent variable
# 2nd, ... argument: parameters to optimise
def linear_model(x: float, slope: float, intercept: float) -> callable:
    return slope * x + intercept


results = optimize.curve_fit(linear_model, df["x"].values, df["y"].values)
popt, _ = results

print(f"Slope = {popt[0]}; Intercept = {popt[1]}")