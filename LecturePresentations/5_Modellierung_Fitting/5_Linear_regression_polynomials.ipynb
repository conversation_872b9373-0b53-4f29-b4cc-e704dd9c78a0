#%%
%matplotlib inline

import importlib

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

f"Pandas version: {pd.__version__}, NumPy version: {np.__version__}"
#%% md
# Linear regression with polynomials
#%% md
## Motivation: Free fall
Laws of motion and gravitation yield the following, general model for free fall:

$$h(t) = h_0 + v_0 t - \frac{1}{2} g t^2$$
#%% md
- if $h_0$, $v_0$, and $g$ are not known, they must be fitted to data.
- note: often some of these *are* known, and a fit is unnecessary
#%%
from utils.utils2 import h, t, visualize_model

figure, metrics = visualize_model(t, h)
plt.show()
#%% md
Rewriting variables, we get $\hat{y}(x) = \beta_0 + \beta_1 x + \beta_2 x^2$.
#%%
def quadratic_model(b_0, b_1, b_2):
    return lambda x: b_0 + b_1 * x + b_2 * x**2
#%% md
### Matrix-Vector formulation
#%% md
With three data points, the matrices then look as follows:
#%% md
$\begin{bmatrix}
\hat{y}_0\\
\hat{y}_1\\
\hat{y}_2\\
\end{bmatrix}
~=~
\begin{bmatrix}
1 & x_0 & x_0^2\\
1 & x_1 & x_1^2\\
1 & x_2 & x_2^2\\
\end{bmatrix}
\cdot \begin{bmatrix}
\beta_0\\
\beta_1\\
\beta_2
\end{bmatrix}
~=~ 
\begin{bmatrix}
y_0\\
y_1\\
y_2\\
\end{bmatrix}
+ 
\begin{bmatrix}
r_0 \\
r_1 \\
r_2 \\
\end{bmatrix}
$
#%% md
- system of three equations
  - three unknowns: parameters $\beta$
  - can be solved exactly if the equations are linearly independent
- The *design matrix* contains the data
  - rows ($x_i$) contain data points
  - columns contain *features*
#%% md
### Why learn how linear regression works?
- a fundamental machine learning problem
- methods for solving are widely applicable
  - support vector machines
  - neural networks / "deep learning"
#%% md
## The general case
#%% md
| jargon | symbol (index notation) | symbol (matrix-vector), with size |
|--------|-------------------------|-----------------------------: |
|predictions|$\hat{y}_i$|$\mathbf{\hat{y}}~\in \mathbb{R}^{N + 1}~~~\,~$ |
|coefficients|$\beta_j$| $\boldsymbol{\beta}~\in \mathbb{R}^{m + 1}~~\,~~$ |
|data matrix / design matrix|$X_{ij}$| $\mathbf{X}~\in \mathbb{R}^{(N + 1) \times (m + 1)}$ |
|observations|$y_i$| $\mathbf{y}~\in \mathbb{R}^{N + 1}~~~~$ |
|(negative) residuals|$r_i$| $\mathbf{r}~\in \mathbb{R}^{N + 1}~~~~$ |
#%% md
### explicit notation

The general case has data $x_i$, $y_i$ with $i=0\dots N$ and parameters $\beta_j$ with $j=0\dots m$:

$\begin{bmatrix}
\hat{y}_0\\
\hat{y}_1\\
\vdots\\
\hat{y}_N
\end{bmatrix}
~=~
\begin{bmatrix}
1 & x_0 & \dots & x_0^m\\
1 & x_1 & \dots & x_1^m\\
\vdots & \vdots & \ddots & \vdots\\
1 & x_N & \dots & x_N^m\\
\end{bmatrix}
~\cdot~ \begin{bmatrix}
\beta_0\\
\beta_1\\
\vdots\\
\beta_m
\end{bmatrix}
~=~ 
\begin{bmatrix}
y_0\\
y_1\\
\vdots\\
y_N\\
\end{bmatrix}
~+~
\begin{bmatrix}
r_0 \\
r_1 \\
\vdots\\
r_N \\
\end{bmatrix}
$
#%% md
We may rewrite this with the *design matrix* (or *data matrix*) $\mathbf{X}$, the coefficient vector $\boldsymbol{\beta}$, the vector of observations $\mathbf{y}$, and the vector of (negative) residuals $\mathbf{r}$:

$\mathbf{X} = 
\begin{bmatrix}
1 & x_0 & \dots & x_0^m\\
1 & x_1 & \dots & x_1^m\\
\vdots & \vdots & \ddots & \vdots\\
1 & x_N & \dots & x_N^m\\
\end{bmatrix};
~~~~ \boldsymbol{\beta} = 
\begin{bmatrix}
\beta_0\\
\beta_1\\
\vdots\\
\beta_m
\end{bmatrix};
~~~~ \mathbf{y} = 
\begin{bmatrix} y_0\\y_1\\\vdots\\y_N\end{bmatrix};
~~~~ \mathbf{r} = 
\begin{bmatrix}r_0\\ r_1 \\\vdots\\ r_N
\end{bmatrix}$
#%% md
### index notation
$$\hat{y}_i = \sum_{j=0}^m X_{ij} \cdot \beta_j = y_i + r_i$$
#%% md
### matrix-vector notation
$$\mathbf{X} \cdot \boldsymbol{\beta} - \mathbf{y} = \mathbf{r}$$
#%% md
## The normal equations

Assuming $\mathbf{X}^T\cdot \mathbf{X}$ is invertible, the parameters $\boldsymbol{\tilde{\beta}}$ which minimize the mean squared error are:
$$\boldsymbol{\tilde{\beta}} = \left(\mathbf{X}^T\cdot \mathbf{X}\right)^{-1} \cdot \mathbf{X}^T \cdot \mathbf{y}$$
#%% md
### Derivation
- deriving the normal equations is possible in index notation or matrix-vector notation
  - the steps are exactly the same.
  - the notations express the same maths, but they look slightly different

#%% md
#### Index formulation
- we want to minimize the mean squared error $J(\beta)$:\
$$J(\beta) = \frac{1}{N} \|\mathbf{r}\|^2 = \frac{1}{N} \sum_{i=0}^N r_i^2$$
#%% md
- substituting $r_i$, this gives\
$$J(\beta) = \frac{1}{N} \sum_{i=0}^N \left(\sum_{j=0}^m \left(X_{ij} \cdot \beta_j\right) - y_i\right)^2$$
#%% md
- to minimize, derive by $\beta_k$ and set to zero:\
$$0 \overset{!}{=} \frac{\partial}{\partial \beta_k} J(\beta) = \frac{2}{N}\sum_{i=0}^N \left(\sum_{j=0}^m \left(X_{ij} \cdot \beta_j\right) - y_i\right) \cdot X_{ik}$$
#%% md
- multiply $\dfrac{N}{2}$, then add $\sum_{i=0}^N X_{ik} y_i$ on both sides:\
$$\sum_{i=0}^N X_{ik} y_i = \sum_{i=0}^N \sum_{j=0}^m X_{ik} \cdot X_{ij} \cdot \beta_j$$
#%% md
- define $\boldsymbol{\Xi} = (\mathbf{X}^T \mathbf{X})^{-1}$, then multiply:\
$$\sum_{k=0}^m \sum_{i=0}^N \Xi_{jk}X_{ik} y_i = \beta_j$$

This is equivalent to the normal equations given above.
#%% md
#### Matrix-Vector formulation
- we want to minimize the mean squared error $J(\boldsymbol{\beta})$:\
$$J(\boldsymbol{\beta}) = \frac{1}{N} \|\mathbf{r}\|^2 = \frac{1}{N} \mathbf{r}^T \cdot \mathbf{r}$$
#%% md
- substituting $\mathbf{r}$, this gives\
$$\begin{aligned}J(\beta) &= \frac{1}{N} \left(\mathbf{X} \cdot \boldsymbol{\beta} - \mathbf{y}\right)^T \cdot \left(\mathbf{X} \cdot \boldsymbol{\beta} - \mathbf{y}\right)\\
&=\frac{1}{N} \left(\boldsymbol{\beta}^T \mathbf{X}^T \mathbf{X} \boldsymbol{\beta} - \mathbf{X}^T\boldsymbol{\beta}^T \mathbf{y} - \mathbf{y}^T \mathbf{X}\boldsymbol{\beta} + \mathbf{y}^T \mathbf{y} \right) \end{aligned}$$
#%% md
- to minimize, derive by $\boldsymbol{\beta}$ and set to zero:\
$$0 \overset{!}{=} \frac{\partial}{\partial \boldsymbol{\beta}} J(\boldsymbol{\beta}) = \frac{2}{N} \left(\mathbf{X}^T \mathbf{X} \boldsymbol{\beta} - \mathbf{X}^T \mathbf{y}\right)$$
#%% md
- to minimize, derive by $\boldsymbol{\beta}$ and set to zero:\
$$0 \overset{!}{=} \frac{\partial}{\partial \boldsymbol{\beta}} J(\boldsymbol{\beta}) = \frac{2}{N} \left(\mathbf{X}^T \mathbf{X} \boldsymbol{\beta} - \mathbf{X}^T \mathbf{y}\right)$$
#%% md
- multiply $\dfrac{N}{2}$, then add $\mathbf{X}^T \mathbf{y}$ on both sides:\
$$\mathbf{X}^T \mathbf{y} = \mathbf{X}^T \mathbf{X} \boldsymbol{\beta} $$
#%% md
- multiply with $(\mathbf{X}^T \mathbf{X})^{-1}$:\
$$\left(\mathbf{X}^T\cdot \mathbf{X}\right)^{-1} \cdot \mathbf{X}^T \cdot \mathbf{y} = \boldsymbol{\beta}$$
#%% md
#### Usage
- let us demonstrate the process with simple pandas and numpy commands
- this is not how you would do this in practice!
  - inversion is slow for big matrices
  - explicit calculation is unnecessary with `np.linalg` library methods
#%%
x = pd.DataFrame({'t^0': np.ones(t.size), 't^1': t, 't^2': t**2})
x
#%%
xT = x.T
xT
#%%
xT_x = xT.dot(x)
xT_x
#%%
xT_x_inverse = pd.DataFrame(
    np.linalg.inv(xT_x.values),
    columns=('t^0', 't^1', 't^2'),
    index=('b_0', 'b_1', 'b_2'),
)
xT_x_inverse
#%%
xT_x_inverse_x = xT_x_inverse.dot(xT)
xT_x_inverse_x
#%%
parameters = xT_x_inverse_x.dot(h)
parameters
#%%
figure, metrics = visualize_model(
    t, h, model=quadratic_model(*parameters)
)
#%% md
#### What if $(\mathbf{X}^T \mathbf{X})$ is not invertible?
#%% md
- in the derivation, we assume that $\mathbf{X}^T\mathbf{X}$ is invertible
  - i.e. $\left(\mathbf{X}^T\mathbf{X}\right)^{-1}$ exists
  - not all square matrices have an inverse!

- then the naïve approach will not work
#%% md
##### Why does it happen?

- problem may lie with the data
  - too few data points: want $N \gg m$ data points for a good fit
  - duplicates: data was measured twice at the same point
  - linear dependence: multidimensional data and granular data points

+ problem may lie with the *features*
  + duplicates: e.g. linear term occurs twice
  + linear dependence: e.g. one term for $x$, one term for $x^2$, one term for $x^2 - x$
#%% md
##### What can we do?
- if the problem lies with the features:
  - model is "wrong"
  - correct the model!
- if the problem lies with the data:
  - check for duplicates
  - take the "pseudo-inverse" `np.linalg.pinv`
#%% md
## Application
- inversion of $\mathbf{X}^T \cdot \mathbf{X}$ is unnecessarily costly
- numpy provides solver methods
#%% md
### numpy.linalg.lstsq
- least-squares solver 
- uses singular value decomposition (LAPACK: DGELSD)
- can deal with over- or underdetermined systems
- slower than np.linalg.solve for fully determined system
#%%
np.linalg.lstsq(x.T.dot(x), x.T.dot(h), rcond=-1)[0]
#%% md
### numpy.linalg.solve

- linear equations solver
- uses LU factorization (LAPACK: DGESV)
- requires exactly determined system
  - then faster and more precise
#%%
np.linalg.solve(x.T.dot(x), x.T.dot(h))
#%% md
## Polynomial fits in practice
#%% md
### Polynomial fits with numpy
For polynomial fits, we may use built-in `numpy` functions
- `polyfit` to fits an n-th degree polynomial
- `polyval` evaluates it for visualization
#%%
from numpy import polyfit, polyval

params = polyfit(t, h, 2)
visualize_model(t, h, model=lambda x: polyval(params, x))
#%% md
### n-th order polynomial fits
- we can fit arbitrary polynomials $a_0 + a_1\,x + a_2\,x^2... + a_n\,x^n$
- these are *linear in the coefficients* $a_0$ ... $a_n$
  - linear regression works as described above
#%% md
#### Warning: arbitrary order polynomials
- *all* data can be approximated by a polynomial of sufficient order
#%%
params = polyfit(t, h, 9)
figure, metrics = visualize_model(t, h, model=lambda x: polyval(params, x))
#%% md
- without a scientific model behind it, these polynomial fits are *meaningless*
  - no explanation
  - no (good) predictions
  - not "simple"