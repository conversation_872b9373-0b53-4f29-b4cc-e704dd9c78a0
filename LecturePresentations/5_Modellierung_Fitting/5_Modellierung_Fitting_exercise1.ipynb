#%% md
# DIY: Polynome fitten mit den Normalengleichungen
#%%
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

%matplotlib inline
#%% md
In dieser Übung bauen wir eine Pipeline, um Polynome beliebigen Grades an gegebene Daten zu fitten.
#%% md
Um alle Schritte auf möglichst niedriger Ebene abzubilden, wollen wir hier nur `transpose`, `dot`, und `inv` verwenden. Das ist nur zu Lernzwecken relevant und dient dem besseren Verständnis der Methode. Für praktische Anwendungen existieren performantere Methoden aus `numpy` und `scipy`. Diese sollten in der Praxis angewandt werden, aber auch beim Lernen nur `np.polyfit` aufzurufen, hilft wenig beim Verständnis. Schreiben Sie also eine solche Funktion selbst, mit der folgenden Signatur:

```python
polyfit(x: np.n<PERSON><PERSON>, y: np.n<PERSON><PERSON>, deg: int) -> np.ndarray
```

Für ein Modell $\hat{y}(x) = \sum_{i=0}^{\texttt{deg}} \beta_i\,x^i$ soll die Funktion die Koeffizienten $\beta_i$ so bestimmen, dass der mittlere quadratische Fehler zwischen aus gegebenen Daten $x$ ermittelten Vorhersagen $\hat{y}$ und dazu vorhandenen Beobachtungen $y$ minimal wird. Die Koeffizienten werden als `np.ndarray` in absteigender Reihenfolge, also $\beta_\texttt{deg}, \beta_{\texttt{deg}-1}, \dots, \beta_1, \beta_0$, zurückgegeben.
#%%
### Begin solution


def polyfit(x: np.ndarray, y: np.ndarray, deg: int):
    """Our own np.polyfit. Fits a polynomial of degree `deg` to data.

    x -- independent variables
    y -- dependent variables
    deg -- (non-negative integer) degree of the polynomial to fit

    returns parameters b_n, b_(n-1), ..., b_0, in descending order
    """
    design_matrix = pd.DataFrame({f'x^{i}': x**i for i in range(deg, -1, -1)})

    xT_x = design_matrix.T.dot(design_matrix)
    xT_y = design_matrix.T.dot(y)

    return np.linalg.inv(xT_x.values).dot(xT_y)


### End solution
#%% md
Prüfen Sie für zufällig erzeugte Daten, ob Ihre Ergebnisse mit den Ergebnissen von `np.polyfit` übereinstimmen:
#%%
np.random.seed(65)
x, y = np.random.randn(2, 10)

for i in range(5):
    np.testing.assert_almost_equal(polyfit(x, y, i), np.polyfit(x, y, i))
#%% md
Stellen Sie die Polynome gemeinsam mit den Daten grafisch dar.
#%%
### Begin solution

fig, ax = plt.subplots(1)
ax.scatter(x, y)

index = np.linspace(x.min(), x.max(), 100)
pd.DataFrame(
    {f'{deg=}': np.polyval(polyfit(x, y, deg), index) for deg in range(5)}, index=index
).plot.line(ax=ax)

### End solution
#%% md
 