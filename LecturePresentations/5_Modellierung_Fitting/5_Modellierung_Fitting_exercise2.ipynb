#%% md
# Fitting an elephant
#%% md
> With four parameters I can fit an elephant, and with five I can make him wiggle his trunk.

— <PERSON>
#%%
import numpy as np
import pandas as pd
import scipy
from matplotlib import pyplot as plt

%matplotlib inline
#%% md
In the spirit of <PERSON>'s statement we will fit an elephant in this exercise.

Jokes aside ... We will determine the coefficients of a polynomial from a linear fit to some data. Given a polynomial $p^{(n)}(x)$ of some order $n$ and some data points $\{(x_i, y_i), i = 1,\dots,N\}$ ($N$: number of pairs of independent and dependent variables) we attempt to solve for the coefficients in several ways:

- Set up the design matrix $\mathbf{X}$ and solve the resulting linear system of equations in the least square sense with an appropriate `scipy` function.
- Determine the coefficients by using `polyfit` from the `numpy.polynomial.polynomial` module.
- Determine the coefficients by using the `curve_fit` function from the `scipy.optimize` module.

The polynomial has the following form
$$
p^{(n)}(x) = \sum\limits_{k=0}^n a_k f_k(x),
$$
where the functions $f_k(x)$ are defined as $f_k(x)= x^k$.
#%%
### BEGIN HIDDEN TESTS

import cv2

image = cv2.cvtColor(cv2.imread('elephantbw2.jpg'), cv2.COLOR_BGR2GRAY)
edges = cv2.Canny(image, threshold1=800, threshold2=1000)

tail = edges.copy()
tail[:, 120:] = 0

back = edges.copy()
back[200:] = 0

front = edges.copy()
front[481:] = 0
front[:, :800] = 0

belly = edges.copy()
belly[:325] = 0
belly[:, :120] = 0
belly[:, 830:] = 0

trunk = edges.copy()
trunk[:481] = 0
trunk[:, :820] = 0

trunk_front = trunk.copy()
trunk_front[590:] = 0
trunk_front[525:, :890] = 0

trunk_back = trunk.copy()
trunk_back[:520] = 0
trunk_back[:, 890:] = 0

top_array = np.concatenate([np.argwhere(i) for i in [tail, back, front, trunk_front]])

bottom_array = np.concatenate([np.argwhere(belly), np.argwhere(trunk_back)])

top = pd.DataFrame(top_array, columns=['y', 'x']).reindex(columns=['x', 'y'])
top['y'] *= -1
bottom = pd.DataFrame(bottom_array, columns=['y', 'x']).reindex(columns=['x', 'y'])
bottom['y'] *= -1

top.sample(200).to_csv('dataset_1.csv', index=False)  # plot.scatter('x', 'y', ax=ax)
bottom.sample(200).to_csv('dataset_2.csv', index=False)  # plot.scatter('x', 'y', ax=ax)

### END HIDDEN TESTS
#%% md
## Preparation
#%% md
Read, inspect, and visualize the two datasets in the files `dataset_1.csv` and `dataset_2.csv`. Which one will we be able to fit the proverbial elephant to?
#%%
### Begin solution

top = pd.read_csv('dataset_1.csv')
bottom = pd.read_csv('dataset_2.csv')

# Visualize this data
ax = top.plot.scatter('x', 'y', style=".", label="top data")
bottom.plot.scatter('x', 'y', style=".", color='red', label="bottom data", ax=ax)
ax.set_xlabel("independent variable $x$")
ax.set_ylabel("dependent variable $y$")
ax.legend()

### End solution
#%% md
## Self-made polynomial fits
Determine the coefficients by setting up the design matrix $\mathbf{X}$. Then solve the linear system of equations in a least square sense by calling a appropriate function from `scipy` (or `numpy`).
#%% md
In the lecture we have learned that we can obtain a linear system of equations based on the design matrix $\mathbf{X}$, the vector dependent variables $\mathbf{y}$, the vector of residuals $\mathbf{r}$ and the vector of unknown coefficients $\boldsymbol{\beta}$.

In matrix-vector notation the linear system of equations looks like this:
$$
\mathbf{X} \cdot \boldsymbol{\beta} - \mathbf{y} = \mathbf{r}
$$

The explicit form in our case is:
$$
\begin{bmatrix}
    f_0(x_1) & f_1(x_1) & \dots & f_n(x_1) \\
    f_0(x_2) & f_1(x_2) & \dots & f_n(x_2) \\
    \vdots   & \vdots  & \ddots & \vdots \\
    f_0(x_N) & f_1(x_N) & \dots & f_n(x_N) \\
\end{bmatrix}
\begin{bmatrix}
    a_0\\
    a_1\\
    \vdots \\
    a_n\\
\end{bmatrix}
- 
\begin{bmatrix}
    y_1\\
    y_2\\
    \vdots \\
    y_N\\
\end{bmatrix}
= 
\begin{bmatrix}
    r_1\\
    r_2\\
    \vdots \\
    r_N\\
\end{bmatrix}
$$

Solving this linear system in the least square sense means that we are seeking $\boldsymbol{\beta}$ such that the (squared) 2-norm $\|\mathbf{X} \cdot \boldsymbol{\beta} - \mathbf{y}\|_2^2$ is minimized.

Fit "elephant polynomials" of different degrees to both datasets and visualize your results. Is it working? Can you explain your results?
#%%
### Begin solution

degree = 6

X_top = np.array([top["x"].values ** n for n in range(degree)]).transpose()
X_bottom = np.array([top["x"].values ** n for n in range(degree)]).transpose()

# For some reason we must explicitly import this submodule. We cannot access it like
# scipy.linalg.lstsq.
from scipy import linalg

solution_top = linalg.lstsq(X_top, top["y"].values)
solution_bottom = linalg.lstsq(X_bottom, bottom["y"].values)


ax = top.plot.scatter("x", "y", style=".", label="top data")
bottom.plot.scatter('x', 'y', style=".", color='red', label="bottom data", ax=ax)

predictions = np.linspace(top.x.min(), top.x.max(), 100)
ax.plot(
    predictions,
    np.polyval(solution_top[0][::-1], predictions),
    color='blue',
    label="top fit",
)
ax.plot(
    predictions,
    np.polyval(solution_bottom[0][::-1], predictions),
    color='red',
    label="bottom fit",
)

ax.set_xlabel("independent variable $x$")
ax.set_ylabel("dependent variable $y$")
plt.legend()

### End solution
#%% md
## Using numpy: `np.polyfit`

Next, use the function [`numpy.polynomial.polynomial.polyfit`](https://numpy.org/doc/stable/reference/generated/numpy.polynomial.polynomial.polyfit.html) to determine the polynomial coefficients.
#%%
### Begin solution

deg = 15
solution_top = np.polynomial.polynomial.polyfit(top.x.values, top.y.values, deg)
solution_bottom = np.polynomial.polynomial.polyfit(
    bottom.x.values, bottom.y.values, deg
)

ax = top.plot.scatter('x', 'y', style=".", label="top data", color='blue')
bottom.plot.scatter('x', 'y', style=".", label="bottom data", ax=ax, color='red')

predictions = np.linspace(top.x.min(), top.x.max(), 100)
ax.plot(
    predictions,
    np.polyval(solution_top[::-1], predictions),
    color='blue',
    label="polyfit -- top",
)
ax.plot(
    predictions,
    np.polyval(solution_bottom[::-1], predictions),
    color='red',
    label="polyfit -- bottom",
)

ax.set_xlabel("independent variable $x$")
ax.set_ylabel("dependent variable $y$")

ax.set_ylim(-700, 0)
plt.legend()

### End solution
#%% md
## Using scipy: `scipy.optimize.curve_fit`

Lastly, we could try to use [`scipy.optimize.curve_fit`](https://docs.scipy.org/doc/scipy/reference/generated/scipy.optimize.curve_fit.html) function to determine the polynomial coefficients. This pretty general method for function optimization requires a callable with one input parameter and any number of parameters to be optimized for, as well as their initial values. Read the documentation, define a polynomial function of arbitrary degree, and optimize the parameters.
#%%
### Begin solution

from scipy import optimize

degree = 11


def p(x, *args):
    return sum(coeff * x**n for n, coeff in enumerate(args))


top_solution = optimize.curve_fit(p, top.x.values, top.y.values, p0=(1,) * degree)
bottom_solution = optimize.curve_fit(
    p, bottom.x.values, bottom.y.values, p0=(1,) * degree
)

ax = top.plot.scatter('x', 'y', style=".", label="top data", color='blue')
bottom.plot.scatter('x', 'y', style=".", label="bottom data", ax=ax, color='red')

predictions = np.linspace(top.x.min(), top.x.max(), 100)
ax.plot(
    predictions,
    np.polyval(solution_top[::-1], predictions),
    label="optimize.curve_fit",
    color='blue',
)
ax.plot(
    predictions,
    np.polyval(solution_bottom[::-1], predictions),
    label="optimize.curve_fit",
    color='red',
)

ax.set_xlabel("independent variable $x$")
ax.set_ylabel("dependent variable $y$")

ax.set_ylim(-700, 0)
plt.legend()

### End solution