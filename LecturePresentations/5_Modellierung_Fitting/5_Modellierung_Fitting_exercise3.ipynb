#%%
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

%matplotlib inline
#%% md
# Pre-Processing
#%% md
Scientific models don't always prescribe a linear or polynomial relationship between independent and dependent variables. Then, those variables need to be transformed accordingly.
#%% md
### Example: Arrhenius plots
#%% md
Perhaps you remember the earlier example on reaction rates. Recall that in chemical reactions, reaction rates $k$ (of unit $\text{s}^{-1}$) as a function of temperature $T$ (Einheit: $\color{red}{\text{K}}$) obey the *Arrhenius law* (with a constant of nature, $R$, and free parameters $k_0$, $E_A$):
$$k = k_0 \cdot \exp\left(\frac{-E_A}{R T}\right)$$

With the following data for $k$ (in $\text{s}^{-1}$) and $T$ (in $\color{red}{^\circ\text{C}}$), determine $k_0$ and $E_A$. Show your results in a $k$ vs. $T$ (that is, linear) plot.
#%%
reaction_rates = pd.DataFrame(
    {'T / °C': (190, 199, 230, 251), 'k / s^-1': (2.52, 5.25, 63, 316)}
)
#%%
### Begin solution

reaction_rates = reaction_rates.assign(
    **{
        '1/T / 1/K': 1 / (reaction_rates['T / °C'] + 273.15),
        'ln(k)': np.log(reaction_rates['k / s^-1']),
    }
)

reaction_rates

minus_e_a_over_r, ln_k0 = np.polyfit(
    reaction_rates['1/T / 1/K'], reaction_rates['ln(k)'], 1
)

predictions = pd.DataFrame(
    {'T': np.linspace(*reaction_rates['T / °C'].agg(['min', 'max']), 100)}
)
predictions['k'] = predictions['T'].apply(
    lambda T: np.exp(ln_k0 + minus_e_a_over_r / (T + 273.15))
)

ax = reaction_rates.plot.scatter('T / °C', 'k / s^-1', label='measured reaction rates')
predictions.plot.line('T', 'k', ax=ax, label='Arrhenius fit')
plt.xlabel('$T~/~\mathrm{^\circ C}$')
plt.ylabel('$k~/~s^{-1}$')

### End solution
#%% md
# Linear regression with more than one dimension
#%% md
### Example: Fitting Neutron and Proton masses
Atomic nuclei are composed of protons and neutrons.
- protons have mass $m_\text{p}$ and have a positive electrical charge
- neutrons have mass $m_\text{n}$ and are electrically neutral.
- the *binding energy* $E_\text{B}$ is released when they bind together in a nucleus
    - by $E = m c^2$, this energy is equivalent to a mass defect
#%% md
Our first goal is to deduce the masses $m_\text{p}$ and $m_\text{n}$ of isolated protons and neutrons.

- We can measure an atomic nucleus:
  - weight $m$
  - number of protons $Z$
  - number of neutrons $N$ (inferred)
- mass can be expressed as\
$$m = Z m_\text{p} + N m_\text{n} - \frac{E_\text{B}(N, Z)}{c^2}$$
- for now, we neglect the binding energy $E_\text{B}$, as $c^2$ is huge
#%% md
- note: This is "slideware"
  - the masses of protons and neutrons are well-established
  - neglecting the binding energy is a bad approximation
  - "cracking a nut with a sledgehammer"
#%%
from download_ame import download_ame2020

mass_data = download_ame2020()
mass_data
#%% md
- $m_\text{p}$, $m_\text{n}$ are already present in the data!
- dataset also contains $E_B$ which could easily let us recover $m_\text{p}$, $m_\text{n}$
- and there are lots of missing values.

Remove these rows and columns.
#%%
### Begin solution

mass_data = (
    mass_data[(mass_data['N'] > 0) & (mass_data['Z'] > 0)]
    .dropna()
    .drop(columns='E_B / keV')
)

mass_data

### End solution
#%% md
Perform a least-squares fit to recover proton and neutron masses. How does the result change if you only take part of your data?
#%%
### Begin solution

x = mass_data.iloc[:100]
y = x.pop('m_tot / u')

np.linalg.lstsq(x.T.dot(x), x.T.dot(y), rcond=-1)[0]

### End solution