import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

np.random.seed(2)
t = pd.Series(0.2 + np.random.rand(10)).cumsum()
t.name = 'time / s'
h = t.apply(lambda x: 5 + 9 * x - 4.9 * x ** 2 + 5 * np.random.randn())
h.name = 'height / m'


def plot_model(
    axis,
    model: callable,
    x_min,
    x_max,
    samples: int = 1000,
    label='prediction',
    color='orange',
):
    """Plots the predictions of a 1D model over a range on an axis"""
    prediction_range = pd.Series(np.linspace(x_min, x_max, samples))
    predictions = prediction_range.transform(model)
    axis.plot(prediction_range, predictions, color=color, label=label)


def calculate_metrics(yerr):
    """Calculates mean error, MAE, and MSE from a series of residuals."""
    metrics = {
        'Mean Absolute Error': abs(yerr).mean(),
        'Mean Squared Error': (yerr ** 2).mean(),
    }
    return metrics


def visualize_model(
    x: pd.Series,
    y: pd.Series,
    model: callable = None,
    label: str = 'prediction',
    samples: int = 1000,
):
    """Plots data from two series and a model."""

    figure, axis = plt.subplots(figsize=(12, 6))

    axis.set_xlabel(x.name)
    axis.set_ylabel(y.name)
    axis.scatter(x, y, label='data')

    metrics = None

    if model is not None:
        plot_model(axis, model, x.min(), x.max(), samples=samples, label=label)

        predictions = x.transform(model)
        residual = y - predictions

        metrics = calculate_metrics(residual)

        axis.legend()

    return figure, metrics
