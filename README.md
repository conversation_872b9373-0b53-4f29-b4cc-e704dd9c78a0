# Mathematische und Naturwissenschaftliche Modellierung -- Computerübung

## About this course

This course is part of the module Mathematische und Naturwissenschaftliche Modellierung.

## Prerequisites

> **NOTE**
> We will handle the prerequisites during the first lesson e.g. creating the required Python environment.

### Git installation

You will need to have the Git version control system installed. For Linux, e.g. a Debian-ish OS like Ubuntu, Git can be installed from the command line with:

```shell
sudo apt-get install git
```

For MacOS, you may use [Homebrew](https://brew.sh/) (see [here](https://formulae.brew.sh/formula/git#default) for details):

```shell
brew install git
```

If you happen to use Windows you can use [git for windows](https://git-scm.com/downloads/win).

### Python installation

In order to follow the course you need a recent version of Python 3 (e.g. Python 3.11.x, 3.12.x, or 3.13.x). You can either download a particular version of Python from [its website](https://www.python.org/downloads/), install it with a package manager (e.g. `apt` on Debian-based Linux distros or `brew` on macOS), or install the Anaconda distribution (this might be the easiest option).

For installing Anaconda, you have several options:

- The [Anaconda Python](https://www.anaconda.com/products/distribution#Downloads) distribution.
- The [Miniconda Python](https://docs.conda.io/en/latest/miniconda.html) distribution.

Please download and [install](https://docs.anaconda.com/anaconda/install/) the distribution for your particular architecture and platform.

### IDE

The usage of an IDE is not required. However to facilitate the setup process we recommend to use the [Visual Studio Code](https://code.visualstudio.com/) (VS Code) text editor. Please use [this](https://code.visualstudio.com/download) link to download the software.

> **NOTE** Of course, you are free to use any other text editor / IDE of your choice. In case you prefer another IDE it will be up to you to configure it to be able to follow the course.

The following extensions will also be helpful:

- [Microsoft Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python) extension
- [Microsoft Python Debugger](https://marketplace.visualstudio.com/items?itemName=ms-python.debugpy) extension
- [Microsoft Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension

You can either use the installation instructions found when following the links or use the "Extensions" button in the options panel on the left-hand side of the VS Code application window to find and install these extensions. Also have a look at the guide to [browse and install extensions](https://code.visualstudio.com/docs/editor/extension-marketplace).

An alternative, more powerful, but also more demanding in Resources (like RAM), IDE is [PyCharm](https://www.jetbrains.com/pycharm/).

## Getting the course material

### From GitLab

You can use VS Code or PyCharm to obtain a _local_ copy of this course repository. Please carefully follow [these](https://docs.gitlab.com/topics/git/clone/#clone-and-open-in-visual-studio-code) instructions to execute the required steps. The GitLab repository is available here: <https://gitlab.ub.uni-giessen.de/ag-heiliger/learn/sose-2025/modellierung.git>

### From VS Code

You can use VS Code to obtain a _local_ copy of this course repository. Please carefully follow [these](https://code.visualstudio.com/docs/sourcecontrol/intro-to-git#_clone-a-repository-locally) instructions to execute the required steps.

> **NOTE** When prompted for the repository URL please enter: `https://gitlab.ub.uni-giessen.de/ag-heiliger/learn/sose-2025/modellierung.git`. You will be asked to choose a folder on your machine where the repository content shall be stored.

### From the command line

From within a terminal emulator of your choice execute in the desired parent directory:

```shell
git clone https://gitlab.ub.uni-giessen.de/ag-heiliger/learn/sose-2025/modellierung.git
```

## Setting up the environment

### With VS Code

Please refer to the instructions how to [set up a _local_ Python environment](https://code.visualstudio.com/docs/python/environments) on your computer with VS code. In particular, have a look at the section ["Creating environments"](https://code.visualstudio.com/docs/python/environments#_creating-environments) for detailed instructions.

You have two options:

- Use the [`pyproject.toml`](./requirements.txt) file to create a _virtual environment_ in your current workspace.
- Use the [`environment.yml`](./environment.yml) file to create a _Conda environment_ in your current workspace.
  > **NOTE** For this you either need a Anaconda or Miniconda installation. Please refer to [these instructions](#python-installation) for installation details.

> **NOTE** Some required packages are (currently) not available to public. To install these packages, you need to replace \<token> in the corresponding file with the token provided via mail. Do not forget to save the file before you install the environment.

### Other options

If you choose to create an environment _outside_ of VS Code (e.g. from the command line or the Anaconda Navigator) you can also use the configuration files listed in the [previous section](#with-vs-code).

With the Anaconda Navigator (quite common on Windows) follow the instructions provided [here](https://docs.anaconda.com/anaconda/navigator/tutorials/manage-environments) and use our [`environment.yml`](./environment.yml) file. Navigate to the section ["Importing an environment"](https://docs.anaconda.com/anaconda/navigator/tutorials/manage-environments/#importing-an-environment), read and execute the instructions of the first paragraph.

If you want to add a such-created environment inside VS Code please read [this](https://code.visualstudio.com/docs/python/environments#_working-with-python-interpreters) section of the documentation.
