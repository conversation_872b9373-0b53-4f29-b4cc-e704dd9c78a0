name: MNWM

channels:
  - conda-forge
  - defaults

dependencies:
  - python=3.12
  # <PERSON>pyter server and plugins
  - jupyter
  - jupyterlab
  - nbgrader~=0.9
  # Scientific stack
  - numpy
  - pandas
  - scipy
  - matplotlib
  - seaborn
  # Testing
  - pytest
  - hypothesis
  # pip packages
  - pip
  - pip:
      # Plugins for students
      - jupyterquiz
      - --extra-index-url https://auth:<token>@gitlab.ub.uni-giessen.de/api/v4/projects/920/packages/pypi/simple
      - Pytest-Nbgrader
variables:
  COURSE_TITLE: "Mathematische und Naturwissenschaftliche Modellierung SoSe 2025"
