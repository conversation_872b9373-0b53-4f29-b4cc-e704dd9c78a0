[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "MNWM"
version = "2025"
description = "Mathematische und Naturwissenschaftliche Modellierung"

requires-python = ">=3.12"
dependencies = [
    "jupyter",
    "jupyterlab",
    "nbgrader>=0.9,<1.0",
    "numpy",
    "pandas",
    "scipy",
    "matplotlib",
    "seaborn",
    "pytest",
    "hypothesis",
    "jupyterquiz",
    "Pytest-Nbgrader @ https://auth:<token>@gitlab.ub.uni-giessen.de/api/v4/projects/920/packages/pypi/files/ab4b993f37125f715610112c862af4cdbc674712b6bdaa46ad1d7d8e0e7de538/pytest_nbgrader-0.0.10-py3-none-any.whl#sha256=ab4b993f37125f715610112c862af4cdbc674712b6bdaa46ad1d7d8e0e7de538",
]

[tool.setuptools]
packages = ["HomeworkAssignments", "LecturePresentations"]
